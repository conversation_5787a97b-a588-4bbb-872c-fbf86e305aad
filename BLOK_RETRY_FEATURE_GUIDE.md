# Blok订单重提功能说明

## 🎯 **功能概述**

为超时失败的Blok订单添加重提功能，允许管理员手动重新提交失败的订单。

## ✨ **功能特性**

### 1. **单个重提**
- 只对状态为"失败不再重提"(status=3)的订单显示"重提"按钮
- 点击后将订单状态改为"未提交"(status=1)
- 清空submit_at时间，让定时任务重新处理

### 2. **批量重提**
- 支持选择多个失败订单进行批量重提
- 自动跳过非失败状态的订单
- 显示详细的处理结果统计

## 🔧 **实现细节**

### **文件结构**
```
app/Admin/Actions/Grid/
├── RetryBlokOrder.php          # 单个重提Action
└── BatchRetryBlokOrders.php    # 批量重提Action
```

### **Controller修改**
- 启用actions列
- 添加条件显示重提按钮
- 配置批量操作

## 📋 **使用方法**

### **单个重提**
1. 在核销记录列表中找到失败的订单
2. 点击操作列的"重提"按钮
3. 确认重提操作
4. 订单状态变为"未提交"，等待定时任务处理

### **批量重提**
1. 勾选需要重提的失败订单
2. 点击"批量重提"按钮
3. 确认批量操作
4. 查看处理结果统计

## 🎨 **界面展示**

### **状态标签**
- 未核销: 黄色(warning)
- 已核销: 绿色(success)  
- 失败不再重提: 红色(danger)

### **操作按钮**
- 只有失败状态的订单才显示"重提"按钮
- 其他状态的订单不显示操作按钮

## 🔄 **处理流程**

```
失败订单 → 点击重提 → 状态改为未提交 → 定时任务检测 → 重新提交
```

## ⚠️ **注意事项**

### **安全检查**
- 重提前检查订单状态，只允许失败订单重提
- 批量操作会自动跳过非失败状态的订单

### **数据更新**
- 重提时清空submit_at时间
- 更新updated_at时间戳
- 状态改为SUBMIT_STATUS_PENDING(1)

### **日志记录**
- 批量操作的错误会记录到日志
- 可通过日志追踪重提操作

## 🧪 **测试场景**

### **测试数据准备**
```sql
-- 创建一些失败状态的测试订单
UPDATE blok_orders 
SET submit_status = 3, submit_at = NOW() 
WHERE id IN (1, 2, 3);
```

### **功能测试**
1. **单个重提测试**
   - 验证只有失败订单显示重提按钮
   - 测试重提操作是否成功
   - 检查状态是否正确更新

2. **批量重提测试**
   - 选择混合状态的订单进行批量重提
   - 验证只有失败订单被处理
   - 检查统计信息是否正确

3. **定时任务测试**
   - 重提后运行定时任务
   - 验证订单是否被重新处理

## 📊 **预期效果**

### **操作便利性**
- 管理员可以方便地重提失败订单
- 支持单个和批量操作
- 操作结果有明确反馈

### **系统稳定性**
- 不影响正常的订单处理流程
- 重提操作有安全检查
- 错误处理完善

### **业务连续性**
- 临时网络问题导致的失败可以快速恢复
- 减少人工干预的复杂度
- 提高订单处理成功率

## 🚀 **扩展可能**

1. **重提次数限制**: 可以添加重提次数记录，避免无限重提
2. **重提原因记录**: 记录重提的原因和操作人
3. **自动重提**: 可以配置某些错误类型的自动重提
4. **重提统计**: 添加重提成功率的统计报表

这个重提功能为Blok订单处理提供了很好的容错和恢复机制！
