<?php
/**
 * 测试双重OrderRealSuccessEvent调用方式的脚本
 */

echo "Testing Dual OrderRealSuccessEvent Implementation...\n";

try {
    echo "\n=== 支持的调用方式 ===\n";
    
    echo "1. Order::dealStatus() 方式:\n";
    echo "   - 传入: ['order_id' => \$order->id]\n";
    echo "   - 触发: event(new OrderRealSuccessEvent(['order_id' => \$order->id]))\n";
    echo "   - 处理: Listener通过order_id查询Order对象\n";
    
    echo "\n2. OrderStatus::startDeal() 方式:\n";
    echo "   - 传入: Order对象\n";
    echo "   - 触发: event(new OrderRealSuccessEvent(\$order))\n";
    echo "   - 处理: Listener直接使用Order对象\n";

    echo "\n=== Event改造 ===\n";
    echo "✓ 修改构造函数参数类型: array|Order \$orderData\n";
    echo "✓ 支持两种数据格式的传入\n";
    echo "✓ 保持向后兼容性\n";

    echo "\n=== Listener改造 ===\n";
    echo "✓ 新增 resolveOrder() 方法\n";
    echo "✓ 自动识别输入数据类型\n";
    echo "✓ 统一处理逻辑\n";

    echo "\n=== 数据流转对比 ===\n";
    echo "Order::dealStatus():\n";
    echo "  \$id → Order::find(\$id) → Event(['order_id' => \$id]) → Listener → Order::find(\$id)\n";
    echo "\n";
    echo "OrderStatus::startDeal():\n";
    echo "  \$order → Event(\$order) → Listener → 直接使用\$order\n";

    echo "\n=== 代码示例 ===\n";
    echo "// 方式1: Order::dealStatus()\n";
    echo "event(new OrderRealSuccessEvent(['order_id' => \$order->id]));\n";
    echo "\n";
    echo "// 方式2: OrderStatus::startDeal()\n";
    echo "event(new OrderRealSuccessEvent(\$order));\n";

    echo "\n=== Listener处理逻辑 ===\n";
    echo "private function resolveOrder(\$orderData): ?Order\n";
    echo "{\n";
    echo "    // 如果直接传入Order对象\n";
    echo "    if (\$orderData instanceof Order) {\n";
    echo "        return \$orderData;\n";
    echo "    }\n";
    echo "    \n";
    echo "    // 如果传入的是数组，包含order_id\n";
    echo "    if (is_array(\$orderData) && isset(\$orderData['order_id'])) {\n";
    echo "        return Order::find(\$orderData['order_id']);\n";
    echo "    }\n";
    echo "    \n";
    echo "    return null;\n";
    echo "}\n";

    echo "\n=== 优势 ===\n";
    echo "✓ 统一的事件处理逻辑\n";
    echo "✓ 减少重复代码\n";
    echo "✓ 提高性能(OrderStatus直接传对象，避免重复查询)\n";
    echo "✓ 保持代码一致性\n";
    echo "✓ 易于维护和扩展\n";

    echo "\n=== 测试要点 ===\n";
    echo "1. 测试Order::dealStatus()触发的Event\n";
    echo "2. 测试OrderStatus命令触发的Event\n";
    echo "3. 验证两种方式都能正确创建blok_orders记录\n";
    echo "4. 检查日志确认处理路径\n";
    echo "5. 确认E客码状态正确更新\n";

    echo "\n=== 文件修改清单 ===\n";
    echo "✓ app/Events/OrderRealSuccessEvent.php - 支持多种输入类型\n";
    echo "✓ app/Listeners/HandleOrderRealSuccessListener.php - 智能解析订单\n";
    echo "✓ app/Console/Commands/OrderStatus.php - 添加Event触发\n";
    echo "✓ app/Models/Order.php - 保持原有Event触发\n";

    echo "\n✓ 双重调用方式改造完成！\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}
