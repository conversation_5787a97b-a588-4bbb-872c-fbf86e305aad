<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

//    ->cron('* * * * *');      自定义 Cron 计划执行任务
//    ->everyMinute();          每分钟执行一次任务
//    ->everyFiveMinutes();     每五分钟执行一次任务
//    ->everyTenMinutes();      每十分钟执行一次任务
//    ->everyFifteenMinutes();  每十五分钟执行一次任务
//    ->everyThirtyMinutes();   每三十分钟执行一次任务
//    ->hourly();               每小时执行一次任务
//    ->hourlyAt(17);           每小时第 17 分钟执行一次任务
//    ->daily();                每天 0 点执行一次任务
//    ->dailyAt('13:00');       每天 13 点执行一次任务
//    ->twiceDaily(1, 13);      每天 1 点及 13 点各执行一次任务
//    ->weekly();               每周日 0 点执行一次任务
//    ->weeklyOn(1, '8:00');    每周一的 8 点执行一次任务
//    ->monthly();              每月第一天 0 点执行一次任务
//    ->monthlyOn(4, '15:00');  每月 4 号的 15 点 执行一次任务
//    ->quarterly();            每季度第一天 0 点执行一次任务
//    ->yearly();               每年第一天 0 点执行一次任务

    /*
    * * * * * cd /data/project/gift_backend/current && php artisan schedule:run >/dev/null 2>&1
    * * * * * cd /data/project/gift_backend/current && flock -xn /data/project/gift_backend/shared/storage/short_schedule.lock -c '/usr/bin/php artisan short-schedule:run --pidfile=/data/project/gift_backend/shared/storage/short-schedule.pid >/dev/null 2>&1'
    */

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //查询订单状态，每分钟一次
//        $this->schedule_command($schedule, 'ap:query-order-ecp', 'everyMinute');//只支持v1
        $this->schedule_command($schedule, 'ap:query-order-v2', 'everyMinute');//支持qcpv1和qcpv2

        //失败重提
        //$this->schedule_command($schedule, 'ap:resubmit-order', 'everyMinute');//只支持v1
        $this->schedule_command($schedule, 'ap:resubmit-order-v2', 'everyMinute');//支持qcpv1和qcpv2

        //兑换码过期处理
        $this->schedule_command($schedule, 'ap:exchange-expired', 'daily');

        //查询支付结果 改为每3分钟查询一次。建行查询会有限制，建议两到三分钟查询一次。
//        $this->schedule_command($schedule, 'trade:query-result', 'everyThreeMinutes');

        //批次码使用情况统计
        //->dailyAt('08:00');
        $this->schedule_command($schedule, 'ap:batch-monitor', 'dailyAt', "08:00");
//        $this->schedule_command($schedule, 'ap:ningde-count', 'dailyAt', "08:01");

        //提交Blok订单到渠道，每5分钟执行一次
        $this->schedule_command($schedule, 'ap:submit-blok-orders', 'everyFiveMinutes');
    }

    protected function shortSchedule(\Spatie\ShortSchedule\ShortSchedule $shortSchedule)
    {
        //提交订单
        //$this->short_schedule_command($shortSchedule, 'ap:submit-order');
        $this->short_schedule_command($shortSchedule, 'ap:submit-order-v2');//支持qcpv1和qcpv2

        //更新主订单状态
        $this->short_schedule_command($shortSchedule, 'ap:update-order-status');

//        //星点值订单处理结果回传
//        $this->short_schedule_command($shortSchedule, 'ap:result-confirm');

        //订单满足发送短信条件，组织短信并写入短信表
        $this->short_schedule_command($shortSchedule, 'ap:order-to-sms_results');

        //短信发送
        $this->short_schedule_command($shortSchedule, 'sms-task:send');

        //测试订单改状态
//        $this->short_schedule_command($shortSchedule, 'ap:test-update-status');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

    /**
     * 内置任务调度封装
     * @param Schedule $schedule
     * @param $command
     * @param string $frequencyMethod
     * @param mixed ...$args
     * @see \Illuminate\Console\Scheduling\Event
     * @see \Illuminate\Console\Scheduling\ManagesFrequencies
     */
    protected function schedule_command(Schedule $schedule, $command, $frequencyMethod = "everyMinute", ...$args)
    {
        //处理定时任务
        $schedule->command($command)
            ->$frequencyMethod(...$args)
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->when(function () use ($command) {
                return $this->allow_run($command);
            })
            ->before(function () use ($command) {
                $command = preg_split("/[\s\r\n]+/", $command)[0];
                Log::debug("command {$command} starting...");
            })
            ->after(function () use ($command) {
                $command = preg_split("/[\s\r\n]+/", $command)[0];
                Log::debug("command {$command} finished.");
            });
    }

    // 调度短时任务
    protected function short_schedule_command(\Spatie\ShortSchedule\ShortSchedule $shortSchedule, $command)
    {
        $shortSchedule->command($command)
            ->everySeconds($this->interval_seconds($command))
            ->withoutOverlapping()
            ->runInBackground()
            ->when(function () use ($command) {
                return $this->allow_run($command);
            });
    }

    //根据配置文件判断该任务是否运行
    protected function allow_run($command)
    {

        $is_allowed = (bool)env("CMD_" . $this->formatCommandStrForKey($command), true);
        if (!$is_allowed) {
            Log::debug("command $command is not allowed run.");
        }
        return $is_allowed;

    }

    //任务运行间隔描述
    protected function interval_seconds($command)
    {

        $seconds = env("CMD_FREQUENCY_" . $this->formatCommandStrForKey($command), 3);
        if ($this->allow_run($command)) {
            Log::debug("command {$command} is allowed run, frequency_seconds is {$seconds}");
        }
        return $seconds;

    }

    protected function formatCommandStrForKey(string $command)
    {
        $formated_str = explode(' ', trim(str_replace(["\r", "\n"], ' ', $command)))[0];
        $formated_str = strtoupper(str_replace([':', '-'], '_', $formated_str));
        return $formated_str;
    }
}
