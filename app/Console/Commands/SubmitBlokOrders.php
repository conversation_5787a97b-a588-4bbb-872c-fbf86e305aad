<?php

namespace App\Console\Commands;

use App\Models\BlokOrders;
use App\Libraries\Channel\BlokOrderChannel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

/**
 * 提交Blok订单到渠道
 */
class SubmitBlokOrders extends SignalAndLockedBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:submit-blok-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将blok_orders表中的数据提交到佰联ok渠道';

    protected $logger;

    protected $min_id = 0;

    protected $once_limit = 50; // 每次处理50条记录

    protected $retry_timeout; // 重试超时时间（分钟）

    protected $blokChannel;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        Command::__construct(); // 测试用生产的时候去掉
        // parent::__construct();
        // $this->lock_ttl = 3600; // 60分钟锁定时间
        $this->logger = Log::channel('blok_orders_submit');
        $this->blokChannel = new BlokOrderChannel();
        $this->retry_timeout = config('blok.retry_timeout', 60);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handleBusiness()
    {
        try {
            // $this->logger->info('开始处理Blok订单提交任务');

            // 获取需要提交的订单
            $this->getMinId();

            if ($this->min_id == 0) {
                // $this->logger->info('没有需要提交的Blok订单');
                return;
            }

            $this->processBlokOrders();

            // $this->logger->info('Blok订单提交任务完成');

        } catch (Exception $e) {
            $this->logger->error('Blok订单提交任务失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取最小ID
     */
    private function getMinId()
    {
        $min_record = BlokOrders::where('submit_status', BlokOrders::SUBMIT_STATUS_PENDING)
            ->orderBy('id')
            ->first(['id']);

        if ($min_record) {
            $this->min_id = $min_record->id;
            // $this->logger->info("找到最小待提交订单ID: {$this->min_id}");
        }
    }

    /**
     * 处理Blok订单
     */
    private function processBlokOrders()
    {
        $processed = 0;
        $success = 0;
        $failed = 0;
        $timeout = 0;

        while (true) {
            // 获取待处理的订单
            $orders = BlokOrders::where('submit_status', BlokOrders::SUBMIT_STATUS_PENDING)
                ->where('id', '>=', $this->min_id)
                ->orderBy('id')
                ->limit($this->once_limit)
                ->get();

            if ($orders->isEmpty()) {
                break;
            }


            foreach ($orders as $order) {
                try {
                    // $this->logger->info("开始处理订单: {$order->id}");

                    // 检查订单是否超时
                    if ($this->isOrderTimeout($order)) {
                        $this->markOrderAsFailed($order, '多次提交失败,不再重试');
                        $timeout++;
                        $processed++;
                        continue;
                    }

                    // 提交订单到渠道
                    $result = $this->blokChannel->submitOrder($order);

                    if ($result['success']) {
                        // 提交成功，更新状态
                        $order->update([
                            'submit_status' => BlokOrders::SUBMIT_STATUS_SUBMITTED,
                            // 核销时间
                            'submit_at'=>Carbon::now()->format('Y-m-d H:i:s')
                        ]);

                        $success++;
                        // $this->logger->info("订单 {$order->id} 提交成功");
                    } else {
                        $failed++;
                        $this->logger->error("订单 {$order->id} 提交失败: " . $result['message']);
                    }

                } catch (Exception $e) {
                    $failed++;
                    $this->logger->error("处理订单 {$order->id} 异常: " . $e->getMessage());
                }

                $processed++;
                $this->min_id = $order->id + 1;
            }

            // 如果处理的记录数少于限制数，说明已经处理完了
            if ($orders->count() < $this->once_limit) {
                break;
            }
        }

        // $this->logger->info("处理完成 - 总计: {$processed}, 成功: {$success}, 失败: {$failed}, 超时: {$timeout}");
    }

    /**
     * 检查订单是否超时
     *
     * @param BlokOrders $order
     * @return bool
     */
    private function isOrderTimeout($order): bool
    {
        $timeoutTime = Carbon::now()->subMinutes($this->retry_timeout);
        return $order->created_at < $timeoutTime;
    }

    /**
     * 标记订单为失败状态
     *
     * @param BlokOrders $order
     * @param string $reason
     */
    private function markOrderAsFailed($order, string $reason = '提交失败')
    {
        try {
            $order->update([
                'submit_status' => BlokOrders::SUBMIT_STATUS_FAILED,
                'submit_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()
            ]);

            $this->logger->info("订单 {$order->id} 标记为失败: {$reason}，创建时间: {$order->created_at}");

        } catch (Exception $e) {
            $this->logger->error("标记订单 {$order->id} 失败状态时出错: " . $e->getMessage());
        }
    }
}
