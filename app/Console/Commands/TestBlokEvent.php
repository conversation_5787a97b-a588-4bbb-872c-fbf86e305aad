<?php

namespace App\Console\Commands;

use App\Events\OrderRealSuccessEvent;
use App\Models\Order;
use App\Models\BlokOrders;
use App\Models\BlokSettings;
use App\Models\BlokCodes;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestBlokEvent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:blok-event {order_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试Blok订单事件处理';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('=== Blok事件测试开始 ===');

        $orderId = $this->argument('order_id');

        if ($orderId) {
            $order = Order::find($orderId);
            if (!$order) {
                $this->error("订单ID {$orderId} 不存在");
                return;
            }
        } else {
            // 自动找一个合适的测试订单
            $order = $this->findTestOrder();
        }

        if (!$order) {
            $this->error('没有找到合适的测试订单');
            $this->showTestDataRequirements();
            return;
        }

        // 显示订单信息
        $this->showOrderInfo($order);

        // 检查前置条件
        if (!$this->checkPreConditions($order)) {
            return;
        }

        // 确认是否继续
        if (!$this->confirm('是否继续执行事件测试？')) {
            $this->info('测试已取消');
            return;
        }

        // 触发事件
        $this->triggerEvent($order);

        // 检查结果
        $this->checkResults($order);

        $this->info('=== Blok事件测试完成 ===');
    }

    /**
     * 查找合适的测试订单
     */
    private function findTestOrder()
    {
        $this->info('正在查找合适的测试订单...');

        $order = Order::where('status', 2) // 已成功的订单
            ->whereHas('order_subs') // 有子订单
            ->whereNotNull('activity_id') // 有活动ID
            ->with(['order_subs', 'order_subs.goods'])
            ->first();

        return $order;
    }

    /**
     * 显示订单信息
     */
    private function showOrderInfo($order)
    {
        $this->info("\n=== 订单信息 ===");
        $this->info("订单ID: {$order->id}");
        $this->info("订单号: {$order->order_no}");
        $this->info("活动ID: {$order->activity_id}");
        $this->info("订单状态: {$order->status}");

        $subOrders = $order->order_subs;
        $this->info("子订单数量: " . $subOrders->count());

        $this->info("\n=== 子订单详情 ===");
        foreach ($subOrders as $index => $subOrder) {
            $goodsName = $subOrder->goods ? $subOrder->goods->goods_name : '未知商品';
            $this->info("子订单" . ($index + 1) . ":");
            $this->info("  ID: {$subOrder->id}");
            $this->info("  商品ID: {$subOrder->goods_id}");
            $this->info("  商品名称: {$goodsName}");
            $this->info("  商品数量: {$subOrder->goods_num}");
            $this->info("  商品价格: {$subOrder->goods_price}");
        }
    }

    /**
     * 检查前置条件
     */
    private function checkPreConditions($order)
    {
        $this->info("\n=== 检查前置条件 ===");

        $allGood = true;

        // 检查BlokSettings配置
        foreach ($order->order_subs as $subOrder) {
            $setting = BlokSettings::where('activity_id', $order->activity_id)
                ->where('goods_id', $subOrder->goods_id)
                ->first();

            if ($setting) {
                $this->info("✅ 找到配置: 活动{$order->activity_id} + 商品{$subOrder->goods_id} → Blok商品{$setting->blok_goods_id}");

                // 检查可用E客码
                $availableCodes = BlokCodes::where('blok_goods_id', $setting->blok_goods_id)
                    ->where('state', 1)
                    ->count();

                if ($availableCodes > 0) {
                    $this->info("✅ 可用E客码数量: {$availableCodes}");
                } else {
                    $this->error("❌ 没有可用的E客码 (blok_goods_id: {$setting->blok_goods_id})");
                    $allGood = false;
                }
            } else {
                $this->error("❌ 未找到配置: 活动{$order->activity_id} + 商品{$subOrder->goods_id}");
                $allGood = false;
            }
        }

        return $allGood;
    }

    /**
     * 触发事件
     */
    private function triggerEvent($order)
    {
        $this->info("\n=== 触发事件 ===");
        $this->info("触发OrderRealSuccessEvent...");

        try {
            event(new OrderRealSuccessEvent($order));
            $this->info("✅ 事件触发成功");
        } catch (\Exception $e) {
            $this->error("❌ 事件触发失败: " . $e->getMessage());
            $this->error("错误详情: " . $e->getTraceAsString());
        }
    }

    /**
     * 检查处理结果
     */
    private function checkResults($order)
    {
        $this->info("\n=== 检查处理结果 ===");

        foreach ($order->order_subs as $index => $subOrder) {
            $this->info("子订单" . ($index + 1) . " (ID: {$subOrder->id}):");

            $blokOrder = BlokOrders::where('sub_order_id', $subOrder->id)->first();

            if ($blokOrder) {
                $this->info("  ✅ 已生成Blok订单");
                $this->info("    Blok订单ID: {$blokOrder->id}");
                $this->info("    E客码: {$blokOrder->ecode}");
                $this->info("    终端号: {$blokOrder->term_id}");
                $this->info("    终端类型: {$blokOrder->term_type}");
                $this->info("    请求ID: {$blokOrder->request_id}");
                $this->info("    提交状态: " . ($blokOrder->submit_status == 1 ? '未提交' : '已提交'));

                // 检查E客码状态
                $blokCode = BlokCodes::where('ecode', $blokOrder->ecode)->first();
                if ($blokCode) {
                    $this->info("    E客码状态: " . ($blokCode->state == 1 ? '未使用' : '已使用'));
                }
            } else {
                $this->error("  ❌ 未生成Blok订单");
            }
        }
    }

    /**
     * 显示测试数据要求
     */
    private function showTestDataRequirements()
    {
        $this->info("\n=== 测试数据要求 ===");
        $this->info("1. 需要有状态为2(成功)的订单");
        $this->info("2. 订单需要有子订单(order_subs)");
        $this->info("3. 订单需要有activity_id");
        $this->info("4. 需要在blok_settings表中配置对应关系");
        $this->info("5. 需要在blok_codes表中有可用的E客码");

        $this->info("\n=== 创建测试数据示例 ===");
        $this->info("-- 检查现有订单");
        $this->info("SELECT id, order_no, activity_id, status FROM orders WHERE status = 2 AND activity_id IS NOT NULL LIMIT 5;");
        $this->info("\n-- 检查配置");
        $this->info("SELECT * FROM blok_settings;");
        $this->info("\n-- 检查E客码");
        $this->info("SELECT blok_goods_id, COUNT(*) as count FROM blok_codes WHERE state = 1 GROUP BY blok_goods_id;");
    }
}
