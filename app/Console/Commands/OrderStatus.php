<?php

namespace App\Console\Commands;

use App\Events\OrderRealSuccessEvent;
use App\Exceptions\MyException;
use App\Exceptions\MyNoLogException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use Exception;
use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\Order;

// use Exception;

/**
 * 订单提交
 */
class OrderStatus extends SignalAndLockedBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:update-order-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新主订单状态';

    protected $min_id = 0;

    protected $once_limit = 200;

    protected $logger;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->lock_ttl = 3600;

        $this->logger = Log::channel('master_order_update');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handleBusiness()
    {
        try {
            //首先查出符合条件的最小id
            $min_id_obj = $this->getMinId();
            if (count($min_id_obj) > 0) {
                //取得最小id值
                $this->min_id = $min_id_obj[0]->id - 1;
            } else {
                throw new MyNoLogException('没有可以处理的订单');
            }

            unset($min_id_obj);

            $orders = $this->getTasks();

            //判断是否有符合条件的订单
            while (count($orders) > 0) {
                $this->startDeal($orders);

                //处理完上一批,再获取下一批符合条件的订单
                $orders = $this->getTasks();
            }
        } catch (MyNoLogException $exc) {
        } catch (MyException $exc) {
            $this->logger->warning($exc->getMessage());
        } catch (\Exception $exc) {
            $this->logger->error($exc->getMessage(), $exc->getTrace());
            Log::error($exc->getMessage(), $exc->getTrace());
        }
    }

    /**
     * 获取要推送的任务
     */
    private function getTasks()
    {
        //3 代表已发货 4-失败不重试，5:失败重试 .订单状态为未推送给业务平台的订单
        return Order::where('status', SysCode::ORDER_STATUS_1)
            ->where('id', '>', $this->min_id)
            ->orderBy('id')
            ->limit($this->once_limit)
            ->get();
    }

    /**
     * 获取最小id
     */
    private function getMinId()
    {
        $days       = intval(config('app.order_submit.days')) + 3; //在提交订单天数基础上再往前加3天。
        $begin_time = date('Y-m-d 00:00:00', strtotime(sprintf('-%d day', $days)));
        return Order::where('status', SysCode::ORDER_STATUS_1)
            ->where('created_at', '>=', $begin_time)
            ->orderBy('created_at')
            ->limit(1)
            ->get();
    }


    /**
     * 开始处理
     */

    private function startDeal($orders)
    {
        foreach ($orders as $order) {

            $this->dealSignal(); //处理信号

            $this->dealCacheTimeout();

            $this->min_id = $order->id;

            $all_success           = true;
            $all_fail              = true;
            $deliver_complete_time = null;

            $sub_order = OrderSub::where('order_id', $order->id)->get();
            if (empty($sub_order)) {
                continue;
            }

            //order_subs -> status: 状态。 1-未处理 2-处理中，3-已发货，4-发货失败，5-失败重提
            //orders -> status: 状态。 1-处理中，2-已发货，3-部分发货，4-发货失败
            foreach ($sub_order as $sub) {
                switch ($sub->status) {
                    case 3:
                        $all_fail = false;
                        if (is_null($deliver_complete_time) || (!empty($sub->deliver_complete_time) && $deliver_complete_time < $sub->deliver_complete_time)) {
                            $deliver_complete_time = $sub->deliver_complete_time;
                        }
                        break;
                    case 4:
                        $all_success = false;
                        break;
                    default:
                        $all_success = false;
                        $all_fail    = false;
                        break;
                }
            }

            if ($all_success) {
                $order->status = SysCode::ORDER_STATUS_2;
            } elseif ($all_fail) {
                $order->status = SysCode::ORDER_STATUS_4;
            }

            if ($order->isDirty()) {

                if ($deliver_complete_time) {
                    $order->deliver_complete_time = $deliver_complete_time;
                }

                $result = $order->save();

                // 如果订单状态变为已发货(成功)，触发OrderRealSuccessEvent
                if ($order->status == SysCode::ORDER_STATUS_2) {
                    try {
                        event(new OrderRealSuccessEvent($order));
                        $this->logger->info("触发OrderRealSuccessEvent: order_id={$order->id}");
                    } catch (\Exception $e) {
                        $this->logger->error("触发OrderRealSuccessEvent失败: " . $e->getMessage());
                    }
                }

                $this->logger->info(json_encode(['order_id' => $order->id, 'order_no' => $order->order_no, 'status' => $order->status, 'result' => $result]));
            }

        }

    }
}
