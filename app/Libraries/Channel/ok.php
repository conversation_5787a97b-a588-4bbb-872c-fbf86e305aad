安付宝 OK聚合支付
API接入说明

v1.1.0
















修订记录
修订日期 动作 修订说明 修订人 版本号
2024.07.14 Create 初始化 胡文杰 1.0.0
2024.07.29 胡文杰 1.0.1
2024.08.06 增加支持外部门店号 胡文杰 1.0.2
2024.08.07 增加销售渠道信息 胡文杰 1.0.3
2024.08.14 修改字段名错误 胡文杰 1.0.4
2024.08.21 增加商品信息请求字段：itemReqAmt,待核销金额。 胡文杰 1.0.5
2024.09.11 增加撤单接口 胡文杰 1.0.6
2024.09.21 券列表中增加销售渠道 胡文杰 1.0.7
2024.09.25 返回商品信息中数量和单价改为可空 胡文杰 1.0.8
2025.05.20 增加业务场景说明 胡文杰 1.1.0


填写说明：
项目名称、项目编号、项目经理、项目总监按照本项目实际情况填写。
文档密级是该文档允许扩散的范围。机密文件、秘密文件必须由风控经理室批准方可借阅；内部文件经一般授权后可由在公司内部和项目组内部传阅；公开文件不需经过授权，可自由进行阅读。
文档主送是指该文档应该主送的对象，项目总监、项目经理是该文档必须主送的对象之一。
文档抄送是指该文档应该抄送的对象，项目管理组是该文档应该抄送的对象之一。
版本号是指该文档的版本次序号，该文档首次发布时可确定为1.0，如果在上一版的基础上有细微的调整和修改，则可在小数点后次版本号加1；如果该文档内容总体上有重大变化或增加/删除了重要章节，则小数点主版本号加1。


目录
目录 3
1 文档说明 6
1.1 目标 6
1.2 阅读对象 6
1.3 数据类型 6
1.4 术语 6
2 安全设计 7
2.1 签名验签 7
2.2 签名方式 7
2.3 报文加解密 7
3 接入方式及数据格式 8
3.1 接入方式 8
3.2 数据格式 8
3.3 字符集 8
3.4 接入地址 8
4 报文头描述 8
4.1.1 请求头 8
4.1.2 返回头 8
5 接口描述 9
5.1 消费接口 9
5.1.1 功能说明 9
5.1.2 访问地址 9
5.1.3 请求字段 9
返回字段 11
5.2 退款接口 13
5.2.1 功能说明 13
5.2.2 访问地址 13
5.2.3 请求字段 13
5.3 消费查询 16
5.3.1 访问地址 16
5.3.2 请求字段 16
5.4 退款查询 18
5.4.1 访问地址 18
5.4.2 请求字段 18
5.4.3 返回字段 19
5.5 关单 20
5.5.1 访问地址 20
5.5.2 请求字段 20
5.5.3 返回字段 21
5.6 优惠买单订单结果通知（外发） 22
5.6.1 访问地址 22
5.6.2 请求字段 22
5.6.3 返回字段 23
6 前置返回码 25

1 文档说明
1.1 目标
本文档对安付宝综合前置受理百联商贸卡券SaaS系统发行的卡、券、优惠买单业务的交易接口规范进行描述。
1.2 阅读对象
本文档的阅读对象是通过本接口开发支付功能的相关工程师。
1.3 数据类型
数据类型 描述
String 字符串类型，取值范围为：字母、数字、符号、中文等
Number 数字类型，取值范围为：数字

1.4 术语
百联商贸的商户使用百联商贸卡券SaaS系统发行的卡、券、优惠买单业务
2 安全设计
2.1 签名验签
通讯报文使用SM2非对称加密算法（SM3WithSM2）进行签名及验签。具体实现如下：请求方使用自己的私钥进行签名，服务方使用请求方的对应的公钥进行验签，返回时做相同处理。
2.2 签名方式
将整个请求或应答报文体的内容作为签名的源数据，将签名的结果放在请求或应答的header中，名为sign。
2.3 报文加解密
发送发将整个报文的数据明文用对方的SM2公钥加密，作为http的body发送。接收方使用自己的私钥对报文进行解密后得到报文明文。

3 接入方式及数据格式
3.1 接入方式
通讯协议https POST方法
3.2 数据格式
JSON字符串
3.3 字符集
UTF-8
3.4 接入地址
测试环境：https://testokfep.okcard.com
测试公钥：
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAECdKCpp7BjwTHtMX6xWs3RkJM289rMQWHkGCPEuoXGKyJLLDiFNbqhMcpfVurMlXtZYq2UMdpgQVld09cxBeMrQ==
生产环境：https://syt.okcard.com
生产公钥：
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEX0LHVyU+jBVOLn+Ekz00gk5JPxHs1UDWPkankfmK77BaTP7CJvjs8Jym+oIeNrb/aMyCRj6cXZ9lBonz4Hq/0w==

4 报文头描述
4.1.1 请求头
参数名称 参数命名 类型 可否为空 说明
接入受理渠道 acqcnl String(32) 不可空 由安付宝分配
请求报文签名 sign String(256) 不可空

4.1.2 返回头
参数名称 参数命名 类型 可否为空 说明
应答报文签名 sign String(256) 不可空


5 接口描述
5.1 消费接口
5.1.1 功能说明
合作方可调用该接口发起卡券消费下单，上送卡券的核销码及订单商品信息。百联商贸接收到消费下单请求后，将按照请求信息进行落单并处理后续卡券消费流程，返回订单结果。
对于优惠买单业务，因其交易场景的原因，百联商贸系统仅会在接口的同步返回中告知合作方下单成功（状态为处理中）或失败，最终交易结果需合作方异步调用百联商贸卡券SaaS进行查询（详见5.3）或接受百联商贸卡券SaaS主动通知（详见5.6）。
5.1.2 访问地址
方法: POST
地址：${domain}/okfep/trans/v5/sale
5.1.3 请求字段
参数名称 参数命名 类型 可否为空 说明
接口类型 service String(32) 不可空 sale
商户号
mercId String(15) 不可空 百联商贸分配给入网商户的编号，生产、测试需联系业务对接人获取
门店号 storeId String(15) 二选一 商户门店在卡券SaaS系统中的编号，由卡券SaaS系统分配
外部门店号 extStoreId String(32) 商户系统中的发起交易的门店号，如需使用外部门店号发起交易，请登录商贸统一商户门户并在对应的门店中配置"商户侧门店编号"

注：若需受理OK烘焙卡交易，必须使用外部门店号
终端号或收银机号 termId String(32) 不可空 无校验
终端类型 termType String(32) 不可空 ECR：收银机
APP：APP
MP：小程序
商户支付订单号 requestId String(32) 不可空 需保证每个商户下唯一，本次消费订单的商户单号
商户购物车订单号 orderNo String(32) 不可空 必填
商户的购物车或收银系统单号；同一笔订单多次发起卡消费扣款或核销时，购物车订单号相同
支付订单总金额 txnAmt Number(12,2) 不可空 单位元，支持小数点后2位
本次消费订单的商品待支付总金额
购物车订单总额 ordAmt Number(12,2) 不可空 购物车订单的总金额（包含已使用其他支付方式完成支付的部分）。
订单结算金额 ordStlAmt Number(12,2) 可空 OK烘焙线上场景专用。
单位元，支持小数点后2位
核销码列表 payCodeList 字符串列表 不可空
--核销码 String（256） 不可空
商品信息 goodsList 商品对象列表 不可空
--购物车行号 itemNo Number 不可空 行号不可重复
--商品名称 itemName String(256) 不可空
--商品品类 itemCategory String(32) 不可空 与商户在配置卡券模板时使用的品类编码同字段
--商品编码 itemUniCode String(32) 不可空 需与商户在配置卡券模板时使用的商品编码同字段
--商品单价 itemPrice Number(12,2) 不可空 单位元，支持小数点后2位
--商品数量 itemNum Number(18,3) 不可空 支持小数点后3位
--商品待核销金额 itemReqAmt Number(12,2) 不可空 单位元，支持小数点后2位
请按照本次交易时实际商品待核销金额传值。
为空时默认为本行商品全额待核销，即默认=商品单价*商品数量，保留两位小数
--商品结算金额 gdsStlAmt Number(12,2) 可空 OK烘焙线上场景专用。
单位元，支持小数点后2位
用户自定对象 userReserved

请求示例

{
"service":"sale",
"mercId":"1000000175",
"storeId":"00056",
"requestId":"241202120622440876",
"txnAmt":0.01,
"ordAmt":15.0,
"payCodeList":["912420935291423676"],
"reqUserReserved":{"a":1,"b":2},
"goodsList":[
{
"itemNo":1,
"itemName": "中华牙膏",
"itemCategory":"123",
"itemUniCode":"88888888",
"itemPrice":"2.25",
"itemNum":"5",
"itemReqAmt": 1
},
{
"itemNo":2,
"itemName": "中华牙膏",
"itemCategory":"123",
"itemUniCode":"88888888",
"itemPrice":"2.25",
"itemNum":"5",
"itemReqAmt": 0.5
}
]
}


5.14 返回字段
参数名称 参数命名 类型 可否为空 说明
返回码 retCode String(8) 不可空 00000000为成功，其它为失败。
返回码描述 retMessage String(256) 不可空
商户号 mercId Sting(15) 不可空 与请求报文相同
门店号 storeId String(15) 可空 与请求报文相同
外部门店号 extStoreId String(32) 可空
商户支付订单号 requestId Sting(32) 不可空 与请求报文相同
卡券SaaS单号 tradeNo String（24） 可空 本次交易在商贸卡券SaaS系统单号
订单状态 txnStatus String(1) 不可空 S：成功，F：失败，U：未知，P：处理中

优惠买单业务场景中，P代表落单成功，订单处于待支付状态，需合作方异步调用【消费查询接口】查询订单状态，或接收交易结果异步通知
实际支付金额 realPay number(12,2) 可空 本次核销或扣款金额，订单状态为成功时返回
注：商户需关注该金额
剩余未核销金额 remainAmt Number(12,2) 可空 券核销成功后返回，卡/优惠买单场景为空
支付渠道 payChnl String(32) 可空 KQSAAS:卡券SAAS系统
支付方式 payType String(32) 可空 Card：卡、 Coupo：券、Discount： 优惠买单
支付渠道侧时间 payTime String（14） 可空 yyyyMMddhhmmss，商贸系统本次返回的时间
销售渠道ID saleChnlId String(32) 可空 卡、券场景成功后返回，处理中不返回。
卡券在商贸平台售出的销售渠道信息，若为商户自发行（非通过商贸平台售出），销售渠道返回商户自身
销售渠道全称 saleChnlName String(256) 可空
销售渠道简称 saleChnlAlias String(64) 可空
卡号 pan String（19） 可空 卡消费成功后返回，券、优惠买单场景为空
交易后卡余额 balanceAmt number(12,2) 可空
券信息列表 payCodeInfoList 券信息列表 可空 券核销成功后返回，卡、优惠买单场景为空
--券号 couponId String（256） 不可空
--销售渠道ID saleChnlId String(32) 可空 一单多券核销时，从该处取值，每张券的销售渠道可能不同。
本次交易使用的卡券销售渠道，若为商户自发行（非通过商贸平台售出），则返回的渠道为商户自身
--销售渠道全称 saleChnlName String(256) 可空
--销售渠道简称 saleChnlAlias String(64) 可空
商品信息 goodsList 商品对象列表 可空
--购物车行号 itemNo Number 不可空
--商品名称 itemName String(256) 不可空
--商品品类 itemCategory String(32) 可空
--商品编码 itemUniCode String(32) 不可空
--商品单价 itemPrice Number(12,2) 可空
--商品数量 itemNum Number(18,3) 可空
--商品实际核销金额 itemAmt Number(12,2) 不可空
--商品实际核销数量 itemCnt Number（12,3） 不可空
用户自定义字段 userReserved 用户自定义对象

响应示例

{
"balanceAmt":0.00,
"extStoreId":"SH146",
"goodsList":[
{
"itemAmt":100.00,
"itemCategory":"A",
"itemCnt":0.595,
"itemName":"芒果莓莓蛋糕6号D",
"itemNo":1,
"itemNum":1,
"itemPrice":168.0,
"itemReqAmt":168.0,
"itemUniCode":"AB0074"
}
],
"mercId":"1000000168",
"payChnl":"KQSAAS",
"payCodeInfoList":[
{
"balanceAmt":"0",
"cardNo":"8900000000350",
"payCode":"890044874207675889",
"saleChnlAlias":"安付宝",
"saleChnlId":"2000000021",
"saleChnlName":"安付宝商务有限公司"
}
],
"payTime":"20250415192728",
"realPay":100.00,
"remainAmt":68.00,
"requestId":"SH1460220250415192731",
"retCode":"00000000",
"retMessage":"交易成功",
"saleChnlAlias":"安付宝",
"saleChnlId":"2000000021",
"saleChnlName":"安付宝商务有限公司",
"tradeNo":"250415100000370",
"txnAmt":100.00,
"txnStatus":"S"
}




5.2 退款接口
5.2.1 功能说明
合作方可调用该接口对原卡、券、优惠买单消费订单发起退款或核销撤销交易。百联商贸接收到退款下单请求后，将按照请求信息进行落单并处理后续交易流程，返回订单结果。
对于优惠买单业务，因其交易场景的原因，百联商贸退款成功后，自动将退款信息通知采购方，由采购方处理资金退款流程，实际退款到账时效根据采购方的处理方式决定。
注：因支付方式限制，券交易仅可整单全额退款，卡/优惠买单支持多次部分退款。
5.2.2 访问地址
方法: POST
地址：${domain}/okfep/trans/v5/refund
5.2.3 请求字段
参数名称 参数命名 类型 可否为空 说明
接口类型 service String(32) 不可空 refund
商户号 mercId String(15) 不可空 同消费接口要求
门店号 storeId String(15) 二选一
外部门店号 extStoreId String(32)
终端号或收银机号 termId String(32) 不可空
终端类型 termType String(32) 不可空 ECR:收银机、APP:APP,MP:小程序
商户退款订单号 requestId String(32) 可空 商户本次退款交易单号，需保证唯一
原购物车订单号 orderNo String(32) 不可空 即消费接口中商户上送的orderNo
原消费支付订单号 orgRequestId String(32) 不可空 即消费接口中商户上送的requestId
原消费交易系统流水号 tradeNo String(32) 可空 原消费交易返回的卡券SaaS单号tradeNo
支付订单退款金额 txnAmt Number(12,2) 不可空 本次需退款金额。
券场景只支持全额退款，卡/优惠买单场景支持多次部分退款。
用户自定义字段 userReserved 用户自定义对象


请求示例

{
"service":"refund",
"mercId":"1000000175",
"storeId":"00056",
"requestId":"241202121432200988",
"orgRequestId":"241022164130364398",
"txnAmt":0.01,
"tradeNo":"240729100004100"
}



5.2.4 返回字段

参数名称 参数命名 类型 可否为空 说明
返回码 retCode String(8) 不可空 00000000为成功，其它为失败。
PCS30017 原消费订单可退金额不足
错误信息 retMessage String(256) 不可空
商户号 mercId Sting(15) 不可空 与请求报文相同
门店号 storeId String(15) 可空 与请求报文相同
外部门店号 extStoreId String(32) 可空 与请求报文相同
商户退款订单号 requestId Sting(32) 不可空 与请求报文相同
卡券SaaS单号 tradeNo String（24） 可空 本次交易卡券SaaS系统退款单号
退款后卡余额 balanceAmt number(12,2) 可空 卡交易退款成功后返回
支付渠道 payChnl String(32) 可空 KQSAAS:卡券SAAS系统
支付方式 payType String(32) 可空 Card：卡、 Coupo：券、Discount： 优惠买单
支付渠道侧时间 payTime String（14） 可空 yyyyMMddhhmmss，商贸系统本次返回的时间
券信息列表 payCodeInfoList 可空 券退款后返回
--券号 couponId String（256） 不可空
--销售渠道ID saleChnlId String(32) 可空
--销售渠道全称 saleChnlName String(256) 可空
--销售渠道简称 saleChnlAlias String(64) 可空
商品信息 goodsList 商品对象列表 可空
--购物车行号 itemNo Number 不可空
--商品名称 itemName String(256) 不可空
--商品品类 itemCategory String(32) 可空
--商品编码 itemUniCode String(32) 不可空
--商品单价 itemPrice Number(12,2) 可空
--商品数量 itemNum Number(18,3) 可空
--商品实际退款金额 itemAmt Number(12,2) 不可空
--商品实际退款数量 itemCnt Number（12,3） 不可空
用户自定义字段 userReserved 用户自定义对象

应答示例

{
"extStoreId":"3110100102",
"goodsList":[
{
"itemAmt":3.80,
"itemCnt":1.000,
"itemName":"绿箭劲酷薄荷硬糖茉莉冰茶味20.5克",
"itemNo":1,
"itemUniCode":"428088029"
}
],
"mercId":"1000000155",
"payChnl":"KQSAAS",
"payTime":"20250415140821",
"requestId":"170135288368942",
"retCode":"00000000",
"retMessage":"交易成功",
"tradeNo":"20250415000004041"
}





5.3 消费查询
5.3.1 访问地址
方法: POST
地址：${domain}/okfep/trans/v5/saleQuery
5.3.2 请求字段
参数名称 参数命名 类型 可否为空 说明
接口类型 service String(32) 不可空 saleQuery
商户号 mercId String(15) 不可空
门店号 storeId String(15) 二选一
外部门店号 extStoreId String(32)
终端号或收银机号 termId String(32) 可空
商户支付订单号 requestId String(32) 不可空 商户消费交易时上送的requestId
用户自定义字段 userReserved 用户自定义对象

请求示例


{
"service":"saleQuery",
"mercId":"1000000175",
"storeId":"00056",
"requestId":"241022164130364398"
}


5.3.3 返回字段


参数名称 参数命名 类型 可否为空 说明
返回码 retCode String(8) 不可空 00000000为成功，其它为失败。
错误信息 retMessage String(256) 不可空
原交易返回码 txRetCode String(8) 不可空
原交易错误信息 txRetMessage String(256) 不可空
商户号 mercId Sting(15) 不可空 与请求报文相同
门店号 storeId String(15) 可空 与请求报文相同
外部门店号 extStoreId String(32) 可空
商户支付订单号 requestId Sting(32) 不可空 与请求报文相同
卡券SaaS单号 tradeNo String（24） 可空 本次交易在商贸卡券SaaS系统单号
订单状态 txnStatus String(1) 不可空 S：成功，F：失败，U：未知，P：处理中，T：已关单

优惠买单业务场景中，P代表落单成功，订单处于待支付状态，需合作方异步调用【消费查询接口】查询订单状态，或接收交易结果异步通知
实际支付金额 realPay number(12,2) 可空 本次核销或扣款金额，订单状态为成功时返回
注：商户需关注该金额
剩余未核销金额 remainAmt Number(12,2) 可空 券核销成功后返回，卡/优惠买单场景为空
支付渠道 payChnl String(32) 可空 KQSAAS:卡券SAAS系统
支付方式 payType String(32) 可空 Card：卡、 Coupo：券、Discount： 优惠买单
支付渠道侧时间 payTime String（14） 可空 yyyyMMddhhmmss，商贸系统本次返回的时间
销售渠道ID saleChnlId String(32) 可空 卡、券场景成功后返回，处理中不返回。
卡券在商贸平台售出的销售渠道信息，若为商户自发行（非通过商贸平台售出），销售渠道返回商户自身
销售渠道全称 saleChnlName String(256) 可空
销售渠道简称 saleChnlAlias String(64) 可空
卡号 pan String（19） 可空 卡消费成功后返回，券、优惠买单场景为空
交易后卡余额 balanceAmt number(12,2) 可空
券信息列表 payCodeInfoList 券信息列表 可空 券核销成功后返回，卡、优惠买单场景为空
--券号 couponId String（256） 不可空
--销售渠道ID saleChnlId String(32) 可空 一单多券核销时，从该处取值，每张券的销售渠道可能不同。
本次交易使用的卡券销售渠道，若为商户自发行（非通过商贸平台售出），则返回的渠道为商户自身
--销售渠道全称 saleChnlName String(256) 可空
--销售渠道简称 saleChnlAlias String(64) 可空
商品信息 goodsList 商品对象列表 可空
--购物车行号 itemNo Number 不可空
--商品名称 itemName String(256) 不可空
--商品品类 itemCategory String(32) 可空
--商品编码 itemUniCode String(32) 不可空
--商品单价 itemPrice Number(12,2) 可空
--商品数量 itemNum Number(18,3) 可空
--商品实际核销金额 itemAmt Number(12,2) 不可空
--商品实际核销数量 itemCnt Number（12,3） 不可空
用户自定义字段 userReserved 用户自定义对象

应答示例

{
"extStoreId":"3110100170",
"goodsList":[
{
"itemAmt":15.90,
"itemCnt":1.000,
"itemName":"长沙味道嫩笋炒肉盖浇饭",
"itemNo":1,
"itemUniCode":"428170006"
},
{
"itemAmt":9.90,
"itemCnt":1.000,
"itemName":"留夫鸭素拌菜150g",
"itemNo":2,
"itemUniCode":"222248073"
}
],
"mercId":"1000000155",
"payChnl":"KQSAAS",
"payTime":"20250422120728",
"realPay":25.80,
"requestId":"P143M6666031250422610342637188",
"retCode":"00000000",
"retMessage":"查询成功",
"saleChnlAlias":"瑞祥-福鲤",
"saleChnlId":"2000000156",
"saleChnlName":"江苏工小友福鲤供应链有限公司",
"tradeNo":"20250422000004157",
"txRetCode":"00000000",
"txRetMessage":"交易成功",
"txnAmt":25.80,
"txnStatus":"S"
}


5.4 退款查询
5.4.1 访问地址
方法: POST
地址：${domain}/okfep/trans/v5/refundQuery
5.4.2 请求字段
参数名称 参数命名 类型 可否为空 说明
接口类型 service String(32) 不可空 refundQuery
商户号 mercId String(15) 不可空
门店号 storeId String(15) 二选一
外部门店号 extStoreId String(32)
终端号或收银机号 termId String(32) 可空
商户退款订单号 requestId String(32) 不可空
用户自定义字段 userReserved 用户自定义对象 可空

请求示例


{
"service":"refundQuery",
"mercId":"1000000175",
"storeId":"00056",
"requestId":"240729154659361729"
}



5.4.3 返回字段
参数名称 参数命名 类型 可否为空 说明
返回码 retCode String(8) 不可空 00000000为成功，其它为失败。
错误信息 retMessage String(256) 不可空
原交易返回码 txRetCode String(8) 不可空
原交易错误信息 txRetMessage String(256) 不可空
订单状态 txnStatus String(1) 可空 （S:成功，F:失败，U:未知）
商户号 mercId Sting(15) 不可空 与请求报文相同
门店号 storeId String(15) 可空 与请求报文相同
外部门店号 extStoreId String(32) 可空 与请求报文相同
商户退款订单号 requestId Sting(32) 不可空 与请求报文相同
卡券SaaS单号 tradeNo String（24） 可空 本次交易卡券SaaS系统退款单号
退款后卡余额 balanceAmt number(12,2) 可空 卡交易退款成功后返回
支付渠道 payChnl String(32) 可空 KQSAAS:卡券SAAS系统
支付方式 payType String(32) 可空 Card：卡、 Coupo：券、Discount： 优惠买单
支付渠道侧时间 payTime String（14） 可空 yyyyMMddhhmmss，商贸系统本次返回的时间
券信息列表 payCodeInfoList 可空 券退款后返回
--券号 couponId String（256） 不可空
--销售渠道ID saleChnlId String(32) 可空
--销售渠道全称 saleChnlName String(256) 可空
--销售渠道简称 saleChnlAlias String(64) 可空
商品信息 goodsList 商品对象列表 可空
--购物车行号 itemNo Number 不可空
--商品名称 itemName String(256) 不可空
--商品品类 itemCategory String(32) 可空
--商品编码 itemUniCode String(32) 不可空
--商品单价 itemPrice Number(12,2) 可空
--商品数量 itemNum Number(18,3) 可空
--商品实际退款金额 itemAmt Number(12,2) 不可空
--商品实际退款数量 itemCnt Number（12,3） 不可空
用户自定义字段 userReserved 用户自定义对象


应答示例

{
"mercId":"1000000175",
"payChnl":"KQSAAS",
"payCodeInfoList":[{"cardNo":"8900000022909"}],
"payTime":"20250401150618",
"requestId":"testRefund014",
"retCode":"00000000",
"retMessage":"查询成功",
"storeId":"00056",
"tradeNo":"250401103190766",
"txRetCode":"00000000",
"txRetMessage":"交易成功",
"txnStatus":"S"
}



5.5 关单
5.5.1 功能说明
合作方可调用该接口关闭处于付款中的“优惠买单”消费订单。对于已成功、失败的交易不可关单，接口将返回关单失败。
5.5.2 访问地址
方法: POST
地址：${domain}/okfep/trans/v5/close
5.5.3 请求字段
参数名称 参数命名 类型 可否为空 说明
接口类型 service String(32) 不可空 close
商户号 mercId String(15) 不可空
门店号 storeId String(15) 二选一
外部门店号 extStoreId String(32)
终端号或收银机号 termId String(32) 可空
商户支付订单号 requestId String(32) 不可空
用户自定义字段 userReserved 用户自定义对象 可空

请求示例


{
"service":"close",
"mercId":"1000000175",
"storeId":"00056",
"extStoreId":NULL,
"requestId":"240920212351948648",
}


5.5.4 返回字段
参数名称 参数命名 类型 可否为空 说明
返回码 retCode String(8) 不可空 00000000为成功，其它为失败。
PCS30019 订单已成功，不可关单。需发起退款操作
PCS30020 订单已关单，无需关单
错误信息 retMessage String(256) 不可空
关单结果 closeStatus String(1) 可空 S:成功，F:失败
商户号 mercId Sting(15) 不可空 与请求报文相同
门店号 storeId String(15) 可空 与请求报文相同
外部门店号 extStoreId String(32) 可空 与请求报文相同
商户支付订单号 requestId String(32) 不可空 与请求报文相同
卡券SaaS单号 tradeNo String（24） 可空 本次消费交易在商贸卡券SaaS系统单号
用户自定义字段 userReserved 用户自定义对象


返回示例

{
"closeStatus":"S",
"mercId":"1000000175",
"payChnl":"KQSAAS",
"requestId":"6828f05c095e41b2aadb3e3dff6ff18a",
"retCode":"00000000",
"retMessage":"交易成功",
"tradeNo":"20250401000059584"
}


5.6 优惠买单订单结果通知（外发）
5.6.1 访问地址
方法: POST
地址：由优惠买单发行方提供
5.6.2 请求字段
参数名称 参数命名 类型 可否为空 说明
商户号 mercId String(15) 不可空
支付渠道 payChnl String(32) 不可空 KQSAAS:卡券SAAS系统
LHKQ:联华卡券系统
OKPAY:安付宝卡包
OKCARD:OK卡
LHPAY:联华卡包
卡券SaaS单号 tradeNo String（24） 可空 本次消费交易在商贸卡券SaaS系统单号
商户支付订单号 requestId String(32) 不可空
原交易返回码 txRetCode String(8) 不可空
原交易错误信息 txRetMessage String(256) 不可空
实际支付金额 realPay number(12,2) 可空 本次核销或扣款金额，订单状态为成功时返回
注：商户需关注该金额
订单状态 txnStatus String(1) 可空 （S:成功，F:失败，T:已关单）
销售渠道ID saleChnlId String(32) 可空
销售渠道全称 saleChnlName String(256) 可空
销售渠道简称 saleChnlAlias String(64) 可空
商品信息 goodsList 商品对象列表 可空
--购物车行号 itemNo Number 不可空
--商品名称 itemName String(256) 不可空
--商品品类 itemCategory String(32) 可空
--商品编码 itemUniCode String(32) 不可空
--商品单价 itemPrice Number(12,2) 可空
--商品数量 itemNum Number(18,3) 可空
--商品实际核销金额 itemAmt Number(12,2) 不可空
--商品实际核销数量 itemCnt Number（12,3） 不可空


请求示例


{
"goodsList":[
{
"itemAmt":5.0,
"itemCnt":1,
"itemName":"中华牙膏",
"itemNo":1,
"itemUniCode":"s0000000245"
}
],
"mercId":"1000000175",
"payChnl":"KQSAAS",
"realPay":5.0,
"requestId":"241119082248271219",
"tradeNo":"241022103188647",
"txRetCode":"PCS00000",
"txRetMessage":"支付成功",
"txnAmt":5.0,
"txnStatus":"S"
}




5.6.3 返回字段
参数名称 参数命名 类型 可否为空 说明
返回码 retCode String(8) 不可空 SUCCESS为成功，其它为失败。
错误信息 retMessage String(256) 不可空

应答示例


{
"retCode":"SUCCESS",
"retMessage":"发送成功"
}





6 前置返回码
返回码 返回信息
00000000 交易成功
ERFE0001 终端验签失败
ERFE0002 主机返回报文验签错误
ERFE0003 前置数据错误
ERFE0004 主机通讯失败
ERFE0005 主机返回报文解析失败
ERFE0006 终端未登记
ERFE0007 数据库操作错误
ERFE0008 交易处理失败
ERFE0009 前置签到失败
ERFE0010 主机签到失败
ERFE0011 不支持该交易类型
ERFE0012 报文格式错误
ERFE0013 门店状态异常
ERFE0014 渠道非法
ERFE0015 解析密码错误
ERFE0016 终端没有退款权限
ERFE0017 密码格式错误
ERFE0018 条码长度不正确
ERFE0019 更新下载参数失败
ERFE0020 获取应用失败
ERFE0021 前置签名失败
ERFE0022 终端加密算法设置错误
ERFE0023 原交易记录不存在
ERFE0024 卡片数据错误
ERFE0025 商户未关联任何卡主体
ERFE0026 流水号重复
ERFE0027 无法确定支付机构
ERFE0028 当前受理渠道不支持该商户
ERFE0029 当前受理渠道不支持该交易
ERFE0030 当前商户不支持该交易
ERFE0031 当前门店不支持该交易
ERFE0032 原交易状态不正确
ERFE0033 不支持部分退货
PCS10001 插入失败
PCS10002 更新失败
PCS10003 记录重复
PCS10004 交易处理中
PCS20001 卡状态异常
PCS20002 卡已过期
PCS20003 非发行商户,不支持使用
PCS20004 门店不存在
PCS20005 门店状态异常
PCS20006 非受理门店
PCS20007 门店不支持此模板
PCS20008 商户不存在
PCS20008 商户未开通卡券saas产品
PCS20009 余额不足
PCS20010 商户订单已存在
PCS20011 商户状态异常
PCS20012 商户用户已存在
PCS20013 账单记录不存在
PCS20014 单笔交易最支持20张券
PCS20015 券信息为空
PCS20016 商户用户号和内部用户号不能都为空
PCS20017 用户不存在
PCS20018 原消费不存在
PCS20019 原流水状态不成功，不允许退款
PCS20020 可退金额不足
PCS20021 机构组不存在
PCS20022 订单处理中
PCS20023 不支持不同模板的券一起核销
PCS20024 卡/券未到时使用时间
PCS20025 模板不存在
PCS20026 该模板仅可使用一张券
PCS20027 该时段不可用
PCS20028 账单为空，不支持下载!
PCS20029 门店号与原订单的不一致
PCS20030 商户号与原订单的不一致
PCS20031 交易失败
PCS20032 该订单已经完成撤销
PCS20033 原流水业务类型错误
PCS20034 券状态异常
PCS20035 超出有效期
PCS20036 原消费订单明细不存在
PCS20100 上传文件失败
PCS20101 Excel文件生成失败
PCS20102 请求参数错误
PCS20103 今日无待跑账单
PCS20104 发行商户必传
PCS20200 查询请求参数错误
PCS20201 商户用户交易不存在
PCS20202 商户错误"
PCS30000 订单重复
PCS30001 订单处理中
PCS30002 商品信息为空
PCS30003 商品数量超限
PCS30004 记录重复
PCS30005 买单码不存在
PCS30006 买单码状态有误
PCS30007 买单码所属商户有误
PCS30008 买单码已失效
PCS30009 门店非模板的所属机构
PCS30010 门店超出适用范围
PCS30011 模板限定门店类型有误
PCS30012 不在可用时段内
PCS30013 可核销商品金额为0
PCS30014 消费订单不存在
PCS30015 原消费订单未成功,不允许退款
PCS30016 退款订单与原消费订单商户不匹配
PCS30017 原消费订单可退金额不足
PCS30018 订单已失败，无需关单
PCS30019 订单已成功，不可关单
PCS30020 订单已关单
PCS30021 退款订单不存在
PCS30022 优惠买单交易不存在
PCS30023 买单码未被扫
PCS30024 买单码已失效
PCS30025 订单异常
PCS30026 买单码请码失败
PCS30101 销售项目不存在
PCS30102 销售项目已关闭
PCS30103 销售项目与商户不匹配
PCS30104 商品不存在
PCS30105 商户优惠买单模板已停用
PCS30106 重复请码请求
PCS30107 请码流水不存在
PCS30108 请码商户不一致
PCS30109 买单码状态有误
PCS30110 订单已关单
PCS30111 订单状态有误
PCS30112 订单信息更新失败
PCS30113 定时间隔通知流水状态有误
PCS30114 定时间隔通知流水剩余次数有误
PCS30115 退款订单不存在
PCS30116 通知地址不存在
PCS30117 采购商公钥不存在
PCS30118 业务加redis锁失败
PCS30119 业务加redis锁异常
PCS30120 响应数据为空
PCS30121 XCP密钥为空
PCS30122 生成签名异常
PCS30123 响应数据解密失败
PCS30123 请求数据加密失败
PCS30124 券核销流水不存在
PCS30125 券核销明细流水不存在
PCS30126 券核销撤销流水不存在
PCS30127 券核销撤销明细流水不存在