<?php

namespace App\Libraries\Channel;

use App\Exceptions\MyException;
use App\Exceptions\UnImplementedException;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

/**
 * 佰联OK渠道例子
 */
class Blok extends BaseChannel
{

    protected $encrypt_type = 'aes';
    protected $sign_type = 'sha256';
    protected $version = '1.0';
    protected $format = 'json';
    protected $charset = 'utf-8';
    protected $aes;

    /**
     * SM2算法实例，用于报文加解密和签名验签
     */
    protected $sm2;

    public function __construct($api_setting)
    {
        parent::__construct($api_setting);

        if (isset($this->api_setting['encrypt_type'])) {
            $this->encrypt_type = $this->api_setting['encrypt_type'];
        }
        if (isset($this->api_setting['sign_type'])) {
            $this->sign_type = $this->api_setting['sign_type'];
        }
        $this->aes = \App\Libraries\Aes::getAes($this->api_setting['encrypt_key'], $this->api_setting['encrypt_iv']);

        // 初始化SM2算法实例，如果需要的话
        if ($this->sign_type == 'sm2' && isset($this->api_setting['private_key']) && isset($this->api_setting['public_key'])) {
            // 这里假设有一个SM2工具类，实际使用时需要根据项目情况调整
            // $this->sm2 = new SM2($this->api_setting['private_key'], $this->api_setting['public_key']);
        }
    }

    public function submit($req_order_no, $channel_product_code, $amount, $user_mobile = '', $charge_account = '', $ext_order_info = [])
    {
        $timestamp1 = time();
        $req_data = [];
        $result = [];
        $url = $this->api_setting['base_url'] ?? '';
        $url .= '/okfep/trans/v5/sale'; // 根据文档 5.1.2 访问地址

        try {
            // if ($amount > 1) {
            //     throw new MyException('数量不能大于1');
            // }

            // 构建商品信息 
            $goods_list = [];
            if (!empty($ext_order_info['goods_list'])) {
                $goods_list = $ext_order_info['goods_list'];
            } else {
                // 默认商品信息
                $goods_list[] = [
                    'itemNo' => 1,
                    'itemName' => $channel_product_code,
                    'itemCategory' => $ext_order_info['item_category'] ?? '',
                    'itemUniCode' => $channel_product_code,
                    'itemPrice' => $amount,
                    'itemNum' => 1,
                    'itemReqAmt' => $amount
                ];
            }

            // 构建核销码列表
            $pay_code_list = [];
            if (!empty($charge_account)) {
                $pay_code_list[] = $charge_account;
            }

            // 构建请求数据，根据文档 5.1.3 请求字段
            $req_data = [
                'service' => 'sale',
                'mercId' => $this->api_setting['appid'],
                'storeId' => $this->api_setting['store_id'] ?? '',
                'extStoreId' => $this->api_setting['ext_store_id'] ?? '',
                'termId' => $this->api_setting['term_id'] ?? '',
                'termType' => $this->api_setting['term_type'] ?? 'ECR',
                'requestId' => $req_order_no,
                'orderNo' => $ext_order_info['order_no'] ?? $req_order_no,
                'txnAmt' => $amount,
                'ordAmt' => $ext_order_info['ord_amt'] ?? $amount,
                'payCodeList' => $pay_code_list,
                'goodsList' => $goods_list
            ];

            // 设置请求头
            $headers = [
                'acqcnl' => $this->api_setting['acqcnl'] ?? '',
                'sign' => ''
            ];

            // 如果需要签名
            if (!empty($this->api_setting['secret_key'])) {
                $headers['sign'] = $this->getSign($req_data, $this->api_setting['secret_key']);
            }

            // 如果需要加密报文体
            $body = $req_data;
            if (!empty($this->api_setting['encrypt_key'])) {
                $body = $this->encrypt($req_data, $this->api_setting['encrypt_key']);
            }

            // 发送HTTP请求
            $http_response = http_request_send($url, $req_data, 'POST', $headers);

            if ($http_response['code'] == 200) {
                // 请求正常响应
                $supplier_result = $http_response['data'];

                // 如果响应是加密的，需要解密
                if (!empty($this->api_setting['encrypt_key']) && !is_array($supplier_result)) {
                    $supplier_result = $this->decrypt($supplier_result, $this->api_setting['encrypt_key']);
                }

                // 如果返回的是字符串，尝试解析为JSON
                if (!is_array($supplier_result)) {
                    $supplier_result = json_decode($supplier_result, true);
                }

                // 验证签名
                if (!empty($supplier_result) && !empty($http_response['headers']['sign']) && !empty($this->api_setting['secret_key'])) {
                    $my_sign = $this->getSign($supplier_result, $this->api_setting['secret_key']);
                    if ($my_sign != $http_response['headers']['sign']) {
                        // 这里不抛出错误，只记录下警告日志以便查询
                        $this->log_submit('warning', $url, $req_data, array_merge($result, [
                            'rsp_sign' => $http_response['headers']['sign'],
                            'my_sign' => $my_sign,
                            'error_msg' => '响应结果验签不正确！',
                        ]), $timestamp1);
                    }
                }

                // 解析返回结果
                $result['return_code'] = $supplier_result['retCode'] ?? '';
                $result['return_msg'] = $supplier_result['retMessage'] ?? '';

                // 根据文档 5.1.4 返回字段处理不同状态
                if ($supplier_result['retCode'] == '00000000') {
                    // 交易成功
                    $result['third_order_no'] = $supplier_result['tradeNo'] ?? '';

                    // 根据订单状态设置结果
                    if ($supplier_result['txnStatus'] == 'S') {
                        // 成功
                        $result['order_status'] = static::ORDER_STATUS_SUCCESS;
                        $result['finish_time'] = $supplier_result['payTime'] ?? '';

                        // 处理卡券信息
                        if (!empty($supplier_result['payCodeInfoList'])) {
                            foreach ($supplier_result['payCodeInfoList'] as $card) {
                                $result['cards'][] = [
                                    'no' => $card['couponId'] ?? '',
                                    'pwd' => '',
                                    'end' => '',
                                ];
                            }
                        }

                        // 设置实际支付金额
                        if (isset($supplier_result['realPay'])) {
                            $result['real_pay'] = $supplier_result['realPay'];
                        }
                    } elseif ($supplier_result['txnStatus'] == 'F') {
                        // 失败
                        $result['order_status'] = static::ORDER_STATUS_FAIL;
                    } elseif ($supplier_result['txnStatus'] == 'P') {
                        // 处理中（优惠买单业务场景）
                        $result['order_status'] = static::ORDER_STATUS_DEALING;
                    } else {
                        // 其他状态当作处理中
                        $result['order_status'] = static::ORDER_STATUS_DEALING;
                    }
                } else {
                    // 返回码不为成功，当作失败处理
                    $result['order_status'] = static::ORDER_STATUS_FAIL;
                }
            } elseif ($http_response['code'] == 28) {
                $result['return_code'] = 'http:28';
                $result['return_msg'] = $http_response['error_msg'] ?? '';
                $result['order_status'] = static::ORDER_STATUS_DEALING;
            } else {
                $result['return_code'] = "http:" . $http_response['code'];
                $result['return_msg'] = $http_response['error_msg'] ?? '';
                $result['order_status'] = static::ORDER_STATUS_FAIL;
            }

            $this->log_submit('info', $url, $req_data, $result, $timestamp1);

        } catch (MyException $exc) {
            // 抛出MyException错误时，可提前设置$result['return_code']，$result['return_msg']，$result['order_status']
            // 否则当处理中
            if (!isset($result['order_status'])) {
                $result['order_status'] = static::ORDER_STATUS_DEALING;
            }
            if (!isset($result['return_code'])) {
                $result['return_code'] = 'waring:600';
            }
            if (!isset($result['return_msg'])) {
                $result['return_msg'] = $exc->getMessage();
            }
            $this->log_submit('error', $url, $req_data, $result, $timestamp1);
        } catch (\Exception $exc) {
            $result['return_code'] = 'error:600';
            $result['return_msg'] = $exc->getMessage();
            $result['order_status'] = static::ORDER_STATUS_FAIL;
            $this->log_submit('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    public function query($req_order_no, $channel_product_code, $ext_order_info = [])
    {
        return [];
    }

    public function getPorducts()
    {
        throw new UnImplementedException();
    }

    public function getBalance()
    {
        return [];
    }

    public function callbackParamsDeal(Request $request)
    {
        // 解析回调参数
        $params = [];
        return $params;
    }

    public function callbackResponse(Request $request, array $callbackParams = null, \Exception $exception = null)
    {
        // 实现回调响应处理
        return [
            'retCode' => 'SUCCESS',
            'retMessage' => '发送成功'
        ];
    }

    //@return array ["code"=>200, "return_code"=>"", "return_msg"=>""]
    public function verify($supplier_product_code, $charge_account, $verify_code = null)
    {
    }

    public function userInfoQueryForTaobao($product_code, $mobile, $verify_code = '')
    {
    }

    /**
     * 权益外放手机号码验证码发放
     * @param $mobile
     * @return array ["code"=>200, "return_code"=>"", "return_msg"=>""]
     * err_code:QUERY_USER_INFO_NUM_NOT_MORE_THAN_ONE   err_msg:手机号码对应淘宝用户数量未超过1
     *          VERIFY_CODE_SYSTEM_ERROR                        验证码系统繁忙，请稍后重试  (3分钟内再次发送验证码会，会返回这个错误)
     * @author: liujq
     * @date: 2023/10/16
     */
    public function sendVerifyCodeForTaobao($product_code, $mobile)
    {
    }
}
