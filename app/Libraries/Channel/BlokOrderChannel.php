<?php

namespace App\Libraries\Channel;

use App\Models\BlokOrders;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Blok订单提交渠道
 * 专门处理blok_orders表数据提交到佰联ok的5.1消费接口
 */
class BlokOrderChannel
{
    protected $logger;
    protected $client;
    protected $config;

    public function __construct()
    {
        $this->logger = Log::channel('blok_orders_submit');
        $this->client = new Client([
            'timeout' => config('blok.timeout', 30),
            'verify' => config('blok.verify_ssl', false)
        ]);

        // 从配置文件获取配置信息
        $this->config = config('blok');
    }

    /**
     * 提交订单到佰联ok渠道
     *
     * @param BlokOrders $order
     * @return array
     */
    public function submitOrder(BlokOrders $order): array
    {
        try {
            // 构建请求数据
            $requestData = $this->buildRequestData($order);

            // 签名
            $signature = $this->signData($requestData);

            // 加密
            $encryptedData = $this->encryptData($requestData);

            // 发送请求
            $response = $this->sendRequest($encryptedData, $signature);

            // 处理响应
            return $this->handleResponse($response, $order);

        } catch (Exception $e) {
            $this->logger->error("提交订单 {$order->id} 失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 构建请求数据
     *
     * @param BlokOrders $order
     * @return array
     */
    private function buildRequestData(BlokOrders $order): array
    {
        return [
            'service' => 'sale',
            'mercId' => $this->config['merc_id'],
            'storeId' => $this->config['store_id'],
            'termId' => $order->term_id,
            'termType' => $order->term_type,
            'requestId' => $order->request_id,
            'orderNo' => $order->order_no,
            'txnAmt' => floatval($order->txn_amt),
            'ordAmt' => floatval($order->ord_amt),
            'payCodeList' => [$order->ecode],
            'goodsList' => [
                [
                    'itemNo' => intval($order->item_no),
                    'itemName' => $order->item_name,
                    'itemCategory' => $order->item_category,
                    'itemUniCode' => $order->item_uni_code,
                    'itemPrice' => floatval($order->txn_amt), // 使用订单金额作为商品单价
                    'itemNum' => intval($order->item_num),
                    'itemReqAmt' => floatval($order->item_req_amt)
                ]
            ]
        ];
    }

    /**
     * 签名数据
     *
     * @param array $data
     * @return string
     */
    private function signData(array $data): string
    {
        // TODO: 实现SM2签名
        // 这里需要实现SM2WithSM3签名算法
        // 暂时返回空字符串，实际使用时需要实现真正的签名逻辑
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);

        $this->logger->info("待签名数据: " . $jsonData);

        // 实际项目中需要使用SM2私钥进行签名
        return 'mock_signature_' . md5($jsonData);
    }

    /**
     * 加密数据
     *
     * @param array $data
     * @return string
     */
    private function encryptData(array $data): string
    {
        // TODO: 实现SM2加密
        // 这里需要使用对方的SM2公钥对数据进行加密
        // 暂时返回JSON字符串，实际使用时需要实现真正的加密逻辑
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);

        $this->logger->info("待加密数据: " . $jsonData);

        // 实际项目中需要使用SM2公钥进行加密
        return $jsonData;
    }

    /**
     * 发送请求
     *
     * @param string $encryptedData
     * @param string $signature
     * @return array
     */
    private function sendRequest(string $encryptedData, string $signature): array
    {
        $url = $this->config['domain'] . '/okfep/trans/v5/sale';

        $headers = [
            'Content-Type' => 'application/json',
            'acqcnl' => $this->config['acqcnl'],
            'sign' => $signature
        ];

        $this->logger->info("发送请求到: " . $url);
        $this->logger->info("请求头: " . json_encode($headers));

        $response = $this->client->post($url, [
            'headers' => $headers,
            'body' => $encryptedData
        ]);

        $responseBody = $response->getBody()->getContents();
        $this->logger->info("响应内容: " . $responseBody);

        return [
            'status_code' => $response->getStatusCode(),
            'body' => $responseBody,
            'headers' => $response->getHeaders()
        ];
    }

    /**
     * 处理响应
     *
     * @param array $response
     * @param BlokOrders $order
     * @return array
     */
    private function handleResponse(array $response, BlokOrders $order): array
    {
        if ($response['status_code'] !== 200) {
            return [
                'success' => false,
                'message' => "HTTP错误: " . $response['status_code']
            ];
        }

        // TODO: 解密响应数据
        // 实际项目中需要使用私钥解密响应数据
        $responseData = json_decode($response['body'], true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => "响应数据格式错误"
            ];
        }

        // 检查业务返回码
        if (isset($responseData['retCode']) && $responseData['retCode'] === '00000000') {
            $this->logger->info("订单 {$order->id} 提交成功", $responseData);
            return [
                'success' => true,
                'message' => '提交成功',
                'data' => $responseData
            ];
        } else {
            $errorMsg = $responseData['retMessage'] ?? '未知错误';
            $this->logger->error("订单 {$order->id} 提交失败: " . $errorMsg, $responseData);
            return [
                'success' => false,
                'message' => $errorMsg,
                'data' => $responseData
            ];
        }
    }

    /**
     * 批量提交订单
     *
     * @param array $orders
     * @return array
     */
    public function batchSubmitOrders(array $orders): array
    {
        $results = [
            'total' => count($orders),
            'success' => 0,
            'failed' => 0,
            'details' => []
        ];

        foreach ($orders as $order) {
            $result = $this->submitOrder($order);
            $results['details'][$order->id] = $result;

            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
            }
        }

        return $results;
    }
}
