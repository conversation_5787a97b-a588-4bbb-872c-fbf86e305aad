<?php
/*
 * @Author: yangy <EMAIL>
 * @Date: 2025-07-07 14:56:30
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2025-07-08 11:13:38
 * @FilePath: /gift_backend/app/Libraries/Enums.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace App\Libraries;

class Enums
{
    const QCPV1 = 'QcpV1';
    const QCPV2 = 'QcpV2';
    //接口版本配置
    public static $channels = [
        self::QCPV1 => 'QcpV1',
        self::QCPV2 => 'QcpV2',
    ];

    //充值验证类型。0-无，1-短信验证码(京东），2-淘宝手机号充值
    const PRE_VERIFY_TYPE_NONE = 0;
    const PRE_VERIFY_TYPE_PRE_VERIFY = 1;
    const PRE_VERIFY_TYPE_TAOBAO = 2;

    public static $pre_verify_type = [
        self::PRE_VERIFY_TYPE_NONE => '无',
        self::PRE_VERIFY_TYPE_PRE_VERIFY => '前置验证(京东直充等）',
        self::PRE_VERIFY_TYPE_TAOBAO => '淘宝手机号充值（天猫购物券）',
    ];
}
