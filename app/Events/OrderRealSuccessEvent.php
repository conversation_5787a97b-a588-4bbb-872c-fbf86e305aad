<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderRealSuccessEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 订单数据
     *
     * @var array|Order
     */
    public $orderData;

    /**
     * 创建一个新的事件实例
     *
     * @param array|Order $orderData 可以是订单ID数组或Order对象
     * @return void
     */
    public function __construct($orderData)
    {
        $this->orderData = $orderData;
    }
}
