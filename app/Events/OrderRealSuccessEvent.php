<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderRealSuccessEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 订单数据
     *
     * @var array
     */
    public $orderData;

    /**
     * 创建一个新的事件实例
     *
     * @param array $orderData
     * @return void
     */
    public function __construct(array $orderData)
    {
        $this->orderData = $orderData;
    }
}
