<?php

namespace App\Listeners;

use App\Events\OrderRealSuccessEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessOrderRealSuccess implements ShouldQueue
{
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderRealSuccessEvent  $event
     * @return void
     */
    public function handle(OrderRealSuccessEvent $event)
    {
        try {
            $biz_content = $event->orderData;
            
            // 获取订单子项
            $orderSubs = DB::table('order_subs')->where('order_id', $biz_content['order_id'])->first();
            
            // 检查是否已经存在记录，避免重复插入
            $existingRecord = DB::table('channel_orders')
                ->where('order_id', $biz_content['order_id'])
                ->first();
                
            if ($existingRecord) {
                Log::info('Channel order record already exists for order ID: ' . $biz_content['order_id']);
                return;
            }

            // 写入新的渠道订单表
            DB::table('channel_orders')->insert([
                'order_id' => $biz_content['order_id'],
                'order_no' => $biz_content['out_trade_no'],
                'amount' => $biz_content['amount'],
                'product_code' => $biz_content['product_code'],
                'status' => 'pending',
                'is_submitted' => false,
                'extra_data' => json_encode([
                    'user_mobile' => $biz_content['user_mobile'] ?? null,
                    'charge_account' => $biz_content['charge_account'] ?? null,
                    'goods_name' => $biz_content['goods_name'] ?? null,
                    'goods_no' => $biz_content['goods_no'] ?? null
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            Log::info('Successfully processed real order success for order ID: ' . $biz_content['order_id']);
        } catch (\Exception $e) {
            Log::error('Failed to process order real success: ' . $e->getMessage(), [
                'order_data' => $event->orderData,
                'exception' => $e
            ]);
            
            // Re-throw the exception to trigger Laravel's retry mechanism
            throw $e;
        }
    }
}
