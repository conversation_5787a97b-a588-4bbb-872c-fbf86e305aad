<?php

namespace App\Listeners;

use App\Events\OrderRealSuccessEvent;
use App\Models\BlokCodes;
use App\Models\BlokOrders;
use App\Models\BlokSettings;
use App\Models\Order;
use App\Models\OrderSub;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleOrderRealSuccessListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param OrderRealSuccessEvent $event
     * @return void
     */
    public function handle(OrderRealSuccessEvent $event)
    {
        try {
            $order = $this->resolveOrder($event->orderData);

            if (!$order) {
                return;
            }

            // 获取子订单（预加载order关联）
            $subOrders = OrderSub::where('order_id', $order->id)
                ->with(['order', 'goods'])
                ->get();
            if ($subOrders->isEmpty()) {
                // Log::info("OrderRealSuccessEvent: 订单 {$order->id} 没有子订单");
                return;
            }

            $this->processBlokOrders($order, $subOrders);

        } catch (\Exception $e) {
            Log::error('OrderRealSuccessEvent处理失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 解析订单对象
     *
     * @param mixed $orderData
     * @return Order|null
     */
    private function resolveOrder($orderData): ?Order
    {
        // 如果直接传入Order对象
        if ($orderData instanceof Order) {
            // Log::info("OrderRealSuccessEvent: 处理Order对象, ID: {$orderData->id}");
            return $orderData;
        }

        // 如果传入的是数组，包含order_id
        if (is_array($orderData) && isset($orderData['order_id'])) {
            $orderId = $orderData['order_id'];
            $order = Order::find($orderId);

            if (!$order) {
                Log::warning("OrderRealSuccessEvent: 订单不存在, ID: {$orderId}");
                return null;
            }

            Log::info("OrderRealSuccessEvent: 处理订单ID: {$orderId}");
            return $order;
        }

        Log::warning('OrderRealSuccessEvent: 无效的订单数据格式', [
            'orderData' => $orderData
        ]);
        return null;
    }

    /**
     * 处理Blok订单逻辑
     *
     * @param Order $order
     * @param \Illuminate\Database\Eloquent\Collection $subOrders
     * @return void
     */
    private function processBlokOrders(Order $order, $subOrders)
    {
        foreach ($subOrders as $subOrder) {
            try {
                // 查找BlokSettings配置
                $blokSetting = BlokSettings::where('activity_id', $order->activity_id)
                    ->where('goods_id', $subOrder->goods_id)
                    ->first();

                if (!$blokSetting) {
                    Log::info("未找到Blok配置: activity_id={$order->activity_id}, goods_id={$subOrder->goods_id}");
                    continue;
                }

                // 查找可用的E客码
                $blokCode = BlokCodes::where('blok_goods_id', $blokSetting->blok_goods_id)
                    ->where('state', BlokCodes::STATE_UNUSED)
                    ->first();

                if (!$blokCode) {
                    Log::warning("没有可用的E客码: blok_goods_id={$blokSetting->blok_goods_id}");
                    continue;
                }

                // 创建BlokOrders记录
                $this->createBlokOrder($order, $subOrder, $blokCode->ecode);

                // 更新E客码状态为已使用
                $blokCode->update(['state' => BlokCodes::STATE_USED]);

                Log::info("成功创建Blok订单: sub_order_id={$subOrder->id}, ecode={$blokCode->ecode}");

            } catch (\Exception $e) {
                Log::error("处理子订单 {$subOrder->id} 的Blok逻辑失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 创建BlokOrders记录
     *
     * @param Order $order
     * @param OrderSub $subOrder
     * @param string $ecode
     * @return void
     */
    private function createBlokOrder(Order $order, OrderSub $subOrder, string $ecode)
    {
        // 获取商品信息
        $goods = $subOrder->goods;
        $goodsName = $goods ? $goods->goods_name : '未知商品';
        $goodsNo = $goods ? $goods->goods_no : '';

        var_dump($subOrder);

        BlokOrders::create([
            'sub_order_id' => $subOrder->id,
            'activity_id' => $order->activity_id, // 直接使用传入的order对象
            'ecode' => $ecode,
            'term_id' => $this->generateTermId(),
            'term_type' => $this->getRandomTermType(),
            'request_id' => $this->generateRequestId($subOrder),
            'order_no' => $subOrder->sub_order_no ?? $subOrder->id,
            'txn_amt' => $subOrder->goods_price ?? 0,
            'ord_amt' => $subOrder->goods_price ?? 0,
            'item_name' => $goodsName,
            'item_no' => $subOrder->id,
            'item_category' => $this->getItemCategory($goods),
            'item_uni_code' => $goodsNo,
            'item_num' => $subOrder->goods_num ?? 1,
            'item_req_amt' => $subOrder->goods_price ?? 0,
            'submit_status' => BlokOrders::SUBMIT_STATUS_PENDING,
        ]);
    }

    /**
     * 生成终端号
     *
     * @return string
     */
    private function generateTermId(): string
    {
        return 'TERM' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 生成请求ID
     *
     * @param OrderSub $subOrder
     * @return string
     */
    private function generateRequestId(OrderSub $subOrder): string
    {
        return 'REQ' . $subOrder->id . '' . time();
    }

    /**
     * 随机获取终端类型
     *
     * @return string
     */
    private function getRandomTermType(): string
    {
        $termTypes = [
            BlokOrders::TERM_TYPE_ECR,
            BlokOrders::TERM_TYPE_APP,
            BlokOrders::TERM_TYPE_MP,
        ];

        return $termTypes[array_rand($termTypes)];
    }

    /**
     * 获取商品品类
     *
     * @param $goods
     * @return string
     */
    private function getItemCategory($goods): string
    {
        if (!$goods) {
            return '未知品类';
        }

        // 根据商品类型返回品类
        $typeMap = [
            1 => '实物商品',
            2 => '虚拟卡密',
            3 => '虚拟直充',
            4 => '实物+虚拟',
            5 => '短连接',
            6 => '家政服务',
        ];

        return $typeMap[$goods->goods_type] ?? '其他';
    }
}
