<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlokSettings extends Model
{
    protected $table = 'blok_settings';

    use DefaultDatetimeFormat;

    protected $fillable = [
        'activity_id',
        'goods_id',
        'ecode',
        'created_by',
        'blok_goods_id',
        'updated_by'
    ];

    /**
     * 关联活动表
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class, 'activity_id', 'id');
    }

    /**
     * 关联商品表
     */
    public function goods(): BelongsTo
    {
        return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }

    /**
     * 关联佰联ok商品表
     */
    public function blokGoods(): BelongsTo
    {
        return $this->belongsTo(BlokGoods::class, 'blok_goods_id', 'id');
    }

    /**
     * 获取活动选项列表
     */
    public static function getActivityOptions()
    {
        return Activity::where('status', 1)->orderBy('activity_name', 'asc')->pluck('activity_name', 'id');
    }

    /**
     * 获取商品选项列表
     */
    public static function getGoodsOptions()
    {
        return Goods::where('status', 1)->orderBy('goods_name', 'asc')->pluck('goods_name', 'id');
    }

    /**
     * 获取佰联ok商品选项列表
     */
    public static function getBlokGoodsOptions()
    {
        return BlokGoods::where('status', 1)->orderBy('name', 'asc')->pluck('name', 'id');
    }

    /**
     * 批量插入数据
     */
    public static function batchInsert(array $data)
    {
        return self::insert($data);
    }
}
