<?php

namespace App\Models;

use App\Models\BL\Project;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlokSettings extends Model
{
    protected $table = 'blok_settings';

    use DefaultDatetimeFormat;

    protected $fillable = [
        'project_name',
        'created_by',
        'updated_by',
        'project_id',
        'goods_name',
        'goods_id',
        'ecode'
    ];

    /**
     * 关联项目表
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class, 'project_id', 'id');
    }

    /**
     * 关联商品表
     */
    public function goods(): BelongsTo
    {
        return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }

    /**
     * 获取项目选项列表
     */
    public static function getProjectOptions()
    {
        return Project::where('status', 1)->orderBy('project_name', 'asc')->pluck('project_name', 'id');
    }

    /**
     * 获取商品选项列表
     */
    public static function getGoodsOptions()
    {
        return Goods::where('status', 1)->orderBy('goods_name', 'asc')->pluck('goods_name', 'id');
    }

    /**
     * 批量插入数据
     */
    public static function batchInsert(array $data)
    {
        return self::insert($data);
    }
}
