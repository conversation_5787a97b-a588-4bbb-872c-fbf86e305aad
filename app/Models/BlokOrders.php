<?php
/*
 * @Author: yangy <EMAIL>
 * @Date: 2025-07-08 10:38:08
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2025-07-09 10:50:22
 * @FilePath: /gift_backend/app/Models/BlokOrders.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlokOrders extends Model
{
    protected $table = 'blok_orders';

    use DefaultDatetimeFormat;

    protected $fillable = [
        'activity_id',
        'sub_order_id',
        'ecode',
        'term_id',
        'term_type',
        'request_id',
        'order_no',
        'txn_amt',
        'ord_amt',
        'item_name',
        'item_no',
        'item_category',
        'item_uni_code',
        'item_num',
        'item_req_amt',
        'submit_status'
    ];

    // 提交状态常量
    const SUBMIT_STATUS_PENDING = 1; // 未提交
    const SUBMIT_STATUS_SUBMITTED = 2; // 已提交
    const SUBMIT_STATUS_FAILED = 3; // 失败不再重提

    public static $submitStatus = [
        self::SUBMIT_STATUS_PENDING => '未提交',
        self::SUBMIT_STATUS_SUBMITTED => '已提交',
        self::SUBMIT_STATUS_FAILED => '失败不再重提',
    ];

    // 终端类型常量
    const TERM_TYPE_ECR = 'ECR';
    const TERM_TYPE_APP = 'APP';
    const TERM_TYPE_MP = 'MP';

    public static $termTypes = [
        self::TERM_TYPE_ECR => 'ECR',
        self::TERM_TYPE_APP => 'APP',
        self::TERM_TYPE_MP => 'MP',
    ];

    /**
     * 关联子订单表
     */
    public function subOrder(): BelongsTo
    {
        return $this->belongsTo(OrderSub::class, 'sub_order_id', 'id');
    }

    /**
     * 关联活动表
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Activity::class, 'activity_id', 'id');
    }

    /**
     * 获取提交状态文本
     */
    public function getSubmitStatusTextAttribute()
    {
        return self::$submitStatus[$this->submit_status] ?? '未知';
    }

    /**
     * 批量插入数据
     */
    public static function batchInsert(array $data)
    {
        return self::insert($data);
    }
}
