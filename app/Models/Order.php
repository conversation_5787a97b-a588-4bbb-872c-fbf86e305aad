<?php

namespace App\Models;


use App\Events\OrderRealSuccessEvent;
use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use Carbon\Carbon;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class Order extends Model
{

    use DefaultDatetimeFormat;

    // 状态。 1-处理中，2-已发货，3-部分发货，4-发货失败
    public static $status = [
        -1 => '无效订单',
        0  => '已取消',
        1  => '处理中',
        2  => '成功',
        3  => '部分成功',
        4  => '失败',
    ];

    //短信状态( 0-不发短信，1-处理中，2-全部成功，3-部分成功，4-全部失败)
    public static $sms_status = [
        0 => '不发短信',
        1 => '处理中',
        2 => '全部成功',
        3 => '部分成功',
        4 => '全部失败',
    ];

    public static $column_list = [
        'activity_id'           => '活动名称',
        'order_time'            => '订单日期',
        'order_no'              => '订单编号',
        'goods_name'            => '商品名称',
        'goods_no'              => '商品编号',
        'goods_price'           => '商品价格',
        'goods_attr'            => '商品属性值',
        'goods_num'             => '购买数量',
        'mobile'                => '登录手机号',
        'user_mobile'           => '用户手机号',
        'charge_account'        => '充值账号',
        'consignee_name'        => '收货人姓名',
        'consignee_phone'       => '收货人联系方式',
        'consignee_address'     => '收货地址',
        'service_date'          => '预约日期',
        'service_time'          => '预约时间',
        'order_remark'          => '订单备注',
        'deliver_complete_time' => '订单发货完成时间',
        'status'                => '订单状态',
        'sms_status'            => '短信状态',
    ];

    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }

    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id');
    }

    public function order_subs()
    {
        return $this->hasMany(OrderSub::class);
    }

    /**
     * 根据子订单状态重置主订单状态。
     * @param $id int 订单id。
     * @return array
     */
    public static function dealStatus($id)
    {

        $ret                 = [];
        $ret['user_id']      = Admin::user()->id;
        $ret['user_name']    = Administrator::where('id', Admin::user()->id)->value('username');
        $ret['order_sub_id'] = $id;

        $order = Order::find($id);
        if (empty($order)) {
            $ret['error_msg'] = '无此订单';
            return $ret;
        }


        $all_success           = true;
        $all_fail              = true;
        $deliver_complete_time = null;

        $sub_order = OrderSub::where('order_id', $order->id)->get();
        //order_subs -> status: 状态。 1-未处理 2-处理中，3-已发货，4-发货失败，5-失败重提
        //orders -> status: 状态。 1-处理中，2-已发货，3-部分发货，4-发货失败
        foreach ($sub_order as $sub) {
            switch ($sub->status) {
                case 3:
                    $all_fail = false;
                    if (is_null($deliver_complete_time) || (!empty($sub->deliver_complete_time) && $deliver_complete_time < $sub->deliver_complete_time)) {
                        $deliver_complete_time = $sub->deliver_complete_time;
                    }
                    break;
                case 4:
                    $all_success = false;
                    break;
                default:
                    $all_success = false;
                    $all_fail    = false;
                    break;
            }
        }

        if ($all_success) {
            $order->status = SysCode::ORDER_STATUS_2;
        } elseif ($all_fail) {
            $order->status = SysCode::ORDER_STATUS_4;
        }

        if ($order->isDirty()) {

            if ($deliver_complete_time) {
                $order->deliver_complete_time = $deliver_complete_time;
            }

            $order->save();

            // 如果订单状态变为已发货(成功)，触发OrderRealSuccessEvent
            if ($order->status == SysCode::ORDER_STATUS_2) {
                try {
                    event(new OrderRealSuccessEvent(['order_id' => $order->id]));
                    Log::info("触发OrderRealSuccessEvent: order_id={$order->id}");
                } catch (\Exception $e) {
                    Log::error("触发OrderRealSuccessEvent失败: " . $e->getMessage());
                }
            }

            $ret['error_msg'] = '重置成功';

            return $ret;
        }

        $ret['error_msg'] = '无需重置';
        return $ret;

    }

    /**
     * 重提该订单下所有失败的子订单
     * @param $id 订单id
     * @return array
     *            Date: 12/3/21
     */
    public static function reSubmitAllSub($id)
    {

        $ret              = [];
        $ret['desc']      = '重提所有失败子订单';
        $ret['user_id']   = Admin::user()->id;
        $ret['user_name'] = Administrator::where('id', Admin::user()->id)->value('username');
        $ret['order_id']  = $id;

        $order = Order::find($id);
        if (empty($order)) {
            $ret['error_msg'] = '无此订单';
            return $ret;
        }

        $subs                = OrderSub::where(['order_id' => $id, 'status' => SysCode::ORDER_SUB_STATUS_4])->get();
        $ret['order_no']     = $order->order_no;
        $ret['sub_order_no'] = implode(',', $subs->pluck('sub_order_no')->toArray());

        //必须是失败的才能重提
        try {

            DB::transaction(function () use ($order) {
                if (DB::table('order_subs')
                    ->where(['order_id' => $order->id, 'status' => SysCode::ORDER_SUB_STATUS_4])
                    ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3])
                    ->update(['status' => SysCode::ORDER_SUB_STATUS_1])
                ) {

                    DB::table('orders')
                        ->where(['id' => $order->id])
                        ->update(['status' => SysCode::ORDER_STATUS_1]);
                }
            });

            $ret['error_msg'] = '重提任务提交成功';

        } catch (\Exception $e) {
            $ret['error_msg'] = $e->getMessage();
            Log::channel('admin_special_optlog')->error($e->getMessage(), $e->getTrace());
            return $ret;
        }

//        Log::channel('optlog')->info(json_encode($ret));//这里不记录日志，在调用该方法的操作里记录。
        return $ret;

    }

    private function ductLastGoods($exchanged_goods)
    {

//        $goods      = explode(',', $exchanged_goods);
//        $last_goods = array_pop($goods);
//        return

    }

    //兑换码订单取消。回滚兑换码状态。
    public static function exchangeRollback($order_id)
    {
        $ret         = [];
        $ret['desc'] = '兑换码订单取消';

        if (Admin::user()) {
            $ret['user_id']   = Admin::user()->id;
            $ret['user_name'] = Administrator::where('id', Admin::user()->id)->value('username');
        }

        $ret['order_id'] = $order_id;

        if (empty($order_id)) {
            throw new MyException('参数错误！');
        }

        if ($order_id instanceof Order) {
            $order    = $order_id;//可能传Model实例进来
            $order_id = $order->id;
        } else {
            $order = Order::find($order_id);
            if (empty($order)) {
                $ret['error_msg'] = '无此订单';
                return $ret;
            }
        }

        if ($order->status != SysCode::ORDER_STATUS_4) {
            throw new MyException('该订单不是失败状态，不能取消！');
        }

        //获取活动信息
        $activity = Activity::find($order->activity_id);

        if (!$activity) {
            throw new MyException('订单异常，请联系客服处理！', SysCode::ACTIVITY_ERROR);
        }

        if (strtotime($activity->end_time) < time()) {
            //兑换时间已经结束
            throw new MyException('订单异常，请联系客服处理！', SysCode::EXCHANGE_HAD_END);
        }

        $activity_class_name = 'App\\Logic\\' . $activity->activity_type;
        if (!class_exists($activity_class_name)) {
            throw new MyException('订单异常，请联系客服处理！', Syscode::ACTIVITY_ERROR);
        }
        $act_logic_obj              = new $activity_class_name();
        $act_logic_obj->activity_id = $activity->id;

        DB::beginTransaction();
        try {

            //更新主子订单表状态为无效订单
            $effect_rows = DB::table('orders')
                ->where(['id' => $order_id, 'status' => SysCode::ORDER_STATUS_4])
                ->update(['status' => SysCode::ORDER_STATUS_DELETED]);
            if (!$effect_rows) {
                throw new MyException('更新订单表状态失败！');
            }

            $effect_rows = DB::table('order_subs')
                ->where(['order_id' => $order_id, 'status' => SysCode::ORDER_SUB_STATUS_4])
                ->update(['status' => SysCode::ORDER_STATUS_DELETED]);
            if (!$effect_rows) {
                throw new MyException('更新子订单表状态失败！');
            }

            if ($effect_rows != DB::table('order_subs')->where(['order_id' => $order_id])->count()) {
                //子订单有非失败状态记录。
                throw new MyException('该订单的子订单并非全部失败！');
            }

            //由活动逻辑实现类进一步做退单处理
            $act_logic_obj->handlerAfterCancelOrder($order);

            DB::commit();
            $ret['error_msg'] = '处理成功！';

//            //重置时减去下单次数限制里的数量
//            static::decrOrderLimit($order);

        } catch (\Exception $e) {

            DB::rollBack();
            Log::error($e);
            $ret['error_msg'] = $e->getMessage();

        }

        return $ret;
    }

    /**
     * 兑换码重置时减去下单次数限制里的数量
     * @param $order           Order 订单
     * @return void
     */
    private static function decrOrderLimit($order)
    {
        try {
            /**
             * @var Redis $redis
             */
            $redis          = app('redis.connection');
            $act            = $order->activity;//活动信息
            $mobile         = $order->mobile;
            $charge_account = $order->charge_account;

            if ($act->activity_type == 'TianMao') {
                //天猫活动兑换次数还原
                $pre_key       = 'tianmao_';
                $key           = $pre_key . md5($charge_account);
                $key_m_account = $pre_key . 'm_' . md5($charge_account);
                $key_m         = $pre_key . $mobile;
                $key_m_m       = $pre_key . 'm_' . $mobile;
                $key_am        = $pre_key . 'am_' . md5($charge_account);
                $key_ma        = $pre_key . 'ma_' . $mobile;

                if (date('Y-m-d', strtotime($order->order_time)) === date('Y-m-d')) {
                    $order_count = $redis->get($key);
                    if (!empty($order_count) && $order_count > 0) {
                        $redis->decr($key);
                        Log::info("order id: {$order->id}, {$act->activity_type} limit: $key = $order_count, decr.");
                    }

                    $order_count = $redis->get($key_m);
                    if (!empty($order_count) && $order_count > 0) {
                        $redis->decr($key_m);
                        Log::info("order id: {$order->id}, {$act->activity_type} limit: $key_m = $order_count, decr.");
                    }
                }

                if (date('Y-m', strtotime($order->order_time)) === date('Y-m')) {
                    $order_count = $redis->get($key_m_m);
                    if (!empty($order_count) && $order_count > 0) {
                        $redis->decr($key_m_m);
                        Log::info("order id: {$order->id}, {$act->activity_type} limit: $key_m_m = $order_count, decr.");
                    }

                    $order_count = $redis->get($key_m_account);
                    if (!empty($order_count) && $order_count > 0) {
                        $redis->decr($key_m_account);
                        Log::info("order id: {$order->id}, {$act->activity_type} limit: $key_m_account = $order_count, decr.");
                    }

                }

                $redis->del($key_am);
                Log::info("order id: {$order->id}, {$act->activity_type}, del key $key_am.");
                $ma_list = implode(', ', $redis->sMembers($key_ma));
                $redis->sRem($key_ma, $charge_account);
                Log::info("order id: {$order->id}, {$act->activity_type} accounts: $key_ma = $ma_list, remove: $charge_account.");

            } else {
                //其它活动兑换次数还原
                if (date('Y-m-d', strtotime($order->order_time)) === date('Y-m-d')) {
                    if ($charge_account) {
                        $pre_key     = $order->activity_id . '_' . $order->goods_id . '_';
                        $key         = $pre_key . md5($charge_account);
                        $order_count = $redis->get($key);
                        if (!empty($order_count) && $order_count > 0) {
                            $redis->decr($key);
                            Log::info("order id: {$order->id}, {$act->activity_type} limit: $key = $order_count, decr.");
                        }
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error("order id: {$order->id}, decr error. " . $e->getMessage(), $e->getTrace());
        }
    }
}
