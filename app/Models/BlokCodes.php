<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlokCodes extends Model
{
    protected $table = 'blok_codes';

    use DefaultDatetimeFormat;

    protected $fillable = [
        'blok_goods_id',
        'ecode',
        'state',
        'created_by'
    ];

    // 状态常量
    const STATE_UNUSED = 1; // 未使用
    const STATE_USED = 2;   // 已使用

    public static $states = [
        self::STATE_UNUSED => '未使用',
        self::STATE_USED => '已使用',
    ];

    /**
     * 关联佰联ok商品表
     */
    public function blokGoods(): BelongsTo
    {
        return $this->belongsTo(BlokGoods::class, 'blok_goods_id', 'id');
    }

    /**
     * 获取佰联ok商品选项列表
     */
    public static function getBlokGoodsOptions()
    {
        return BlokGoods::where('status', 1)->orderBy('name', 'asc')->pluck('name', 'id');
    }

    /**
     * 批量插入数据
     */
    public static function batchInsert(array $data)
    {
        return self::insert($data);
    }

    /**
     * 检查E客码是否已存在
     */
    public static function isEcodeExists($ecode, $blokGoodsId = null)
    {
        $query = self::where('ecode', $ecode);
        if ($blokGoodsId) {
            $query->where('blok_goods_id', $blokGoodsId);
        }
        return $query->exists();
    }

    /**
     * 获取状态文本
     */
    public function getStateTextAttribute()
    {
        return self::$states[$this->state] ?? '未知';
    }
}
