<?php

namespace App\Admin\Actions\Grid;

use App\Models\BlokOrders;
use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class RetryBlokOrder extends RowAction
{
    public $name = '重提';

    public function handle(Model $model, Request $request)
    {
        // 检查订单状态
        if ($model->submit_status != BlokOrders::SUBMIT_STATUS_FAILED) {
            return $this->response()->error('只有失败状态的订单才能重提');
        }

        try {
            // 将状态改为未提交，让定时任务重新处理
            $model->update([
                'submit_status' => BlokOrders::SUBMIT_STATUS_PENDING,
                'submit_at' => null, // 清空提交时间
                'updated_at' => now()
            ]);

            return $this->response()->success('订单已重新加入提交队列')->refresh();

        } catch (\Exception $e) {
            return $this->response()->error('重提失败: ' . $e->getMessage());
        }
    }

    public function dialog()
    {
        $this->confirm('确定要重新提交这个订单吗？', '重提确认', [
            'confirmButtonText' => '确定重提',
            'cancelButtonText' => '取消',
        ]);
    }
}
