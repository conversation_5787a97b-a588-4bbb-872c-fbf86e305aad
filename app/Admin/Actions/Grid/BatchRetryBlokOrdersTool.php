<?php

namespace App\Admin\Actions\Grid;

use App\Models\BlokOrders;
use Encore\Admin\Actions\GridAction;
use Illuminate\Http\Request;

class BatchRetryBlokOrdersTool extends GridAction
{
    public $name = '批量重提失败订单';

    protected $selector = '.grid-batch-retry-blok-orders';

    public function script()
    {
        return <<<SCRIPT

$('.grid-batch-retry-blok-orders').on('click', function() {
    
    // 获取选中的订单ID
    var selected = [];
    $('.grid-row-checkbox:checked').each(function(){
        selected.push($(this).data('id'));
    });
    
    if (selected.length === 0) {
        swal('提示', '请先选择要重提的订单', 'warning');
        return;
    }
    
    swal({
        title: '批量重提确认',
        text: '确定要重提选中的 ' + selected.length + ' 个订单吗？',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dd6b55',
        confirmButtonText: '确定重提',
        cancelButtonText: '取消',
        closeOnConfirm: false
    }, function(){
        
        $.ajax({
            method: 'post',
            url: '{$this->getHandleRoute()}',
            data: {
                _token: LA.token,
                ids: selected
            },
            success: function (data) {
                $.pjax.reload('#pjax-container');
                
                if (typeof data === 'object') {
                    if (data.status) {
                        swal('成功', data.message, 'success');
                    } else {
                        swal('失败', data.message, 'error');
                    }
                }
            }
        });
    });
});

SCRIPT;
    }

    public function handle(Request $request)
    {
        $ids = $request->get('ids');
        
        if (empty($ids)) {
            return $this->response()->error('请选择要重提的订单');
        }

        $successCount = 0;
        $failedCount = 0;
        $skippedCount = 0;

        foreach ($ids as $id) {
            try {
                $order = BlokOrders::find($id);
                
                if (!$order) {
                    $skippedCount++;
                    continue;
                }

                // 只处理失败状态的订单
                if ($order->submit_status != BlokOrders::SUBMIT_STATUS_FAILED) {
                    $skippedCount++;
                    continue;
                }

                // 将状态改为未提交
                $order->update([
                    'submit_status' => BlokOrders::SUBMIT_STATUS_PENDING,
                    'submit_at' => null,
                    'updated_at' => now()
                ]);

                $successCount++;

            } catch (\Exception $e) {
                $failedCount++;
                \Log::error("批量重提订单 {$id} 失败: " . $e->getMessage());
            }
        }

        $message = "批量重提完成！成功: {$successCount}";
        if ($skippedCount > 0) {
            $message .= "，跳过: {$skippedCount}";
        }
        if ($failedCount > 0) {
            $message .= "，失败: {$failedCount}";
        }

        return $this->response()->success($message);
    }

    public function html()
    {
        return <<<HTML
<a class="btn btn-sm btn-warning grid-batch-retry-blok-orders">
    <i class="fa fa-refresh"></i>&nbsp;&nbsp;批量重提失败订单
</a>
HTML;
    }
}
