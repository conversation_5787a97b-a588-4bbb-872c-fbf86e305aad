<?php

namespace App\Admin\Actions\Grid;

use App\Models\BlokOrders;
use Encore\Admin\Actions\BatchAction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BatchRetryBlokOrders extends BatchAction
{
    public $name = '批量重提';

    public function handle(Collection $collection, Request $request)
    {
        $successCount = 0;
        $failedCount = 0;
        $skippedCount = 0;

        foreach ($collection as $model) {
            try {
                // 只处理失败状态的订单
                if ($model->submit_status != BlokOrders::SUBMIT_STATUS_FAILED) {
                    $skippedCount++;
                    continue;
                }

                // 将状态改为未提交
                $model->update([
                    'submit_status' => BlokOrders::SUBMIT_STATUS_PENDING,
                    'submit_at' => null,
                    'updated_at' => now()
                ]);

                $successCount++;

            } catch (\Exception $e) {
                $failedCount++;
                \Log::error("批量重提订单 {$model->id} 失败: " . $e->getMessage());
            }
        }

        $message = "批量重提完成！成功: {$successCount}";
        if ($skippedCount > 0) {
            $message .= "，跳过: {$skippedCount}";
        }
        if ($failedCount > 0) {
            $message .= "，失败: {$failedCount}";
        }

        return $this->response()->success($message)->refresh();
    }

    public function dialog()
    {
        $this->confirm('确定要批量重提选中的失败订单吗？', '批量重提确认', [
            'confirmButtonText' => '确定重提',
            'cancelButtonText' => '取消',
        ]);
    }
}
