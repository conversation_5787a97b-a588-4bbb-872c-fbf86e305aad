<?php

namespace App\Admin\Controllers;

use App\Models\BlokSettings;
use App\Models\BL\Project;
use App\Models\Goods;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Encore\Admin\Widgets;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;

class BlokSettingsController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Blok设置管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new BlokSettings());

        $grid->column('id', __('ID'));
        $grid->column('project_name', __('项目名称'));
        $grid->column('goods_name', __('商品名称'));
        $grid->column('ecode', __('E客码'));
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        // 添加导入按钮
        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<a class="btn btn-sm btn-success" style="float: right;margin-right: 20px;" href="' . url('admin/blok-settings/import') . '"><i class="fa fa-upload"></i>&nbsp;Excel导入&nbsp;</a>');
        });

        $grid->actions(function ($actions) {
            $actions->disableView();
        });

        // 查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 3, function (Filter $filter) {
                $filter->like('project_name', '项目名称');
            });
            $filter->column(1 / 3, function (Filter $filter) {
                $filter->like('goods_name', '商品名称');
            });
            $filter->column(1 / 3, function (Filter $filter) {
                $filter->like('ecode', 'E客码');
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(BlokSettings::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('project_name', __('项目名称'));
        $show->field('goods_name', __('商品名称'));
        $show->field('ecode', __('E客码'));
        $show->field('created_at', __('创建时间'));
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new BlokSettings());

        $form->select('project_id', __('项目'))
            ->options(Project::where('status', 1)->pluck('project_name', 'id'))
            ->required();
        
        $form->select('goods_id', __('商品'))
            ->options(Goods::where('status', 1)->pluck('goods_name', 'id'))
            ->required();
        
        $form->text('ecode', __('E客码'))->required();

        $form->submitted(function (Form $form) {
            $model = $form->model();
            
            // 获取项目和商品信息
            if ($form->project_id) {
                $project = Project::find($form->project_id);
                if ($project) {
                    $model->project_name = $project->project_name;
                }
            }
            
            if ($form->goods_id) {
                $goods = Goods::find($form->goods_id);
                if ($goods) {
                    $model->goods_name = $goods->goods_name;
                }
            }
            
            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;
            }
        });

        return $form;
    }

    /**
     * Excel导入页面
     */
    public function import(Content $content)
    {
        $content->title('Excel导入');
        $form = new Widgets\Form();
        $form->method('post');
        $form->action(url('admin/blok-settings/do-import'));

        $form->select('project_id', '项目')
            ->options(Project::where('status', 1)->pluck('project_name', 'id'))
            ->required();
        
        $form->select('goods_id', '商品')
            ->options(Goods::where('status', 1)->pluck('goods_name', 'id'))
            ->required();

        $form->file('excel_file', 'Excel文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['xls', 'xlsx'],
        ])->required();

        $form->html('<div class="alert alert-info">
            <strong>导入说明：</strong><br/>
            1. 支持 xls、xlsx 格式的Excel文件<br/>
            2. Excel文件只需要一列数据，包含E客码信息<br/>
            3. 第一行为标题行，从第二行开始读取数据<br/>
            4. 请确保数据格式正确，避免导入失败
        </div>');

        $form->disableReset();
        $content->body(new Widgets\Box('', $form));
        return $content;
    }
}
