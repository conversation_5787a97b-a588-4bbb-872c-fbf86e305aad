<?php
/*
 * @Author: yangy <EMAIL>
 * @Date: 2025-07-07 16:12:08
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2025-07-08 10:16:09
 * @FilePath: /gift_backend/app/Admin/Controllers/BlokSettingsController.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace App\Admin\Controllers;

use App\Models\BlokSettings;
use App\Models\Activity;
use App\Models\Goods;
use App\Models\BlokGoods;
use App\Models\ActivityPrize;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BlokSettingsController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '佰联ok设置管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new BlokSettings());

        $grid->column('id', __('ID'));
        $grid->column('activity.activity_name', __('活动名称'))->sortable();
        $grid->column('goods.goods_name', __('商品名称'))->sortable();
        $grid->column('blokGoods.name', __('佰联ok商品'))->sortable();
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        $grid->actions(function ($actions) {
            $actions->disableView();
        });

        // 查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('activity_id', '活动')->select(Activity::where('status', 1)->pluck('activity_name', 'id'));
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('goods_id', '商品')->select(Goods::where('status', 1)->pluck('goods_name', 'id'));
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('blok_goods_id', '佰联ok商品')->select(BlokGoods::where('status', 1)->pluck('name', 'id'));
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(BlokSettings::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('activity.activity_name', __('活动名称'));
        $show->field('goods.goods_name', __('商品名称'));
        $show->field('blokGoods.name', __('佰联ok商品'));
        $show->field('created_at', __('创建时间'));
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new BlokSettings());

        $form->select('activity_id', __('活动'))
            ->options(Activity::where('status', 1)->pluck('activity_name', 'id'))
            ->required()
            ->help('选择要配置的活动');

        $form->select('goods_id', __('商品'))
            ->options(function ($id) {
                if ($id) {
                    // 编辑时，获取当前记录的商品信息
                    $setting = BlokSettings::find($id);
                    if ($setting && $setting->goods) {
                        return [$setting->goods_id => $setting->goods->goods_name];
                    }
                }
                return [];
            })
            ->ajax('/admin/blok-settings/goods-by-activity')
            ->required()
            ->help('请先选择活动，然后选择该活动中配置的商品');

        $form->select('blok_goods_id', __('佰联ok商品'))
            ->options(BlokGoods::where('status', 1)->pluck('name', 'id'))
            ->required()
            ->help('选择对应的佰联ok商品');

        // 表单验证
        $form->saving(function (Form $form) {
            // 检查是否已存在相同的配置
            $exists = BlokSettings::where('activity_id', $form->activity_id)
                ->where('goods_id', $form->goods_id)
                ->where('blok_goods_id', $form->blok_goods_id);

            if ($form->isEditing()) {
                $exists = $exists->where('id', '!=', $form->model()->id);
            }

            if ($exists->exists()) {
                throw new \Exception('该配置组合已存在，请检查后重新提交');
            }
        });

        $form->submitted(function (Form $form) {
            $model = $form->model();

            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;
            }
        });

        return $form;
    }

    /**
     * 根据活动ID获取商品列表
     */
    public function getGoodsByActivity(Request $request)
    {
        $activityId = $request->get('q');

        if (!$activityId) {
            return [];
        }

        $goods = DB::table('activity_prizes')
            ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
            ->where('activity_prizes.activity_id', $activityId)
            ->where('goods.status', 1)
            ->orderBy('activity_prizes.order_by')
            ->orderBy('activity_prizes.id', 'desc')
            ->get(['goods.id', 'goods.goods_name']);

        $ret = [];
        foreach ($goods as $g) {
            $ret[] = ['id' => $g->id, 'text' => $g->goods_name];
        }

        return $ret;
    }
}
