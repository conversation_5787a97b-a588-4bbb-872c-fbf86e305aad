<?php

namespace App\Admin\Controllers;

use App\Models\BlokGoods;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Facades\Admin;

class BlokGoodsController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '佰联OK商品';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new BlokGoods());

        $grid->column('id', __('Id'));
        $grid->column('name', __('商品名称'));
        $grid->column('created_at', __('创建时间'));
        $grid->column('status', __('状态'))->using(BlokGoods::$status)->dot([2 => 'danger', 1 => 'success']);
        return $grid;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new BlokGoods());

        $form->text('name', __('商品名称'));
        $form->hidden('created_by')->value(Admin::user()->id);
        $form->hidden('updated_by')->value(Admin::user()->id);
          $states = [
            'on'  => ['value' => 1, 'text' => '启用', 'color' => 'primary'],
            'off' => ['value' => 0, 'text' => '禁用', 'color' => 'default'],
        ];
        $form->switch('status', '状态')->states($states)->default(1);

        return $form;
    }
}
