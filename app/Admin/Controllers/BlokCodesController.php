<?php
/*
 * @Author: yangy <EMAIL>
 * @Date: 2025-07-07 16:42:52
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2025-07-08 12:02:17
 * @FilePath: /gift_backend/app/Admin/Controllers/BlokCodesController.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace App\Admin\Controllers;

use App\Models\BlokCodes;
use App\Models\BlokGoods;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Encore\Admin\Widgets;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;

class BlokCodesController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '佰联E客码管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new BlokCodes());

        $grid->column('id', __('ID'));
        $grid->column('blokGoods.name', __('佰联ok商品'))->sortable();
        $grid->column('ecode', __('E客码'));
        $grid->column('state', __('状态'))->using(BlokCodes::$states)->label([
            BlokCodes::STATE_UNUSED => 'success',
            BlokCodes::STATE_USED => 'danger',
        ]);
        $grid->column('created_at', __('创建时间'));

        // 添加导入按钮
        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<a class="btn btn-sm btn-success" style="float: right;margin-right: 20px;" href="' . url('admin/blok-codes/import') . '"><i class="fa fa-upload"></i>&nbsp;Excel导入&nbsp;</a>');
        });

        $grid->disableCreateButton();
        $grid->actions(function ($actions) {
            $actions->disableView();
        });

        // 查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('blok_goods_id', '佰联ok商品')->select(BlokGoods::where('status', 1)->pluck('name', 'id'));
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->like('ecode', 'E客码');
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('state', '状态')->select(BlokCodes::$states);
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(BlokCodes::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('blokGoods.name', __('佰联ok商品'));
        $show->field('ecode', __('E客码'));
        $show->field('state', __('状态'))->using(BlokCodes::$states);
        $show->field('created_at', __('创建时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new BlokCodes());

        $form->select('blok_goods_id', __('佰联ok商品'))
            ->options(BlokGoods::where('status', 1)->pluck('name', 'id'))
            ->required();

        $form->text('ecode', __('E客码'))->required();

        $form->select('state', __('状态'))
            ->options(BlokCodes::$states)
            ->default(BlokCodes::STATE_UNUSED)
            ->required();

        $form->submitted(function (Form $form) {
            $model = $form->model();

            if ($form->isEditing()) {
                // 编辑时不更新created_by
            } else {
                $model->created_by = Admin::user()->id;
            }
        });

        return $form;
    }

    /**
     * Excel导入页面
     */
    public function import(Content $content)
    {
        $content->title('Excel导入');
        $form = new Widgets\Form();
        $form->method('post');
        $form->action(url('admin/blok-codes/do-import'));

        $form->select('blok_goods_id', '佰联ok商品')
            ->options(BlokGoods::where('status', 1)->pluck('name', 'id'))
            ->required();

        $form->file('excel_file', 'Excel文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['xls', 'xlsx'],
        ])->required();

        $form->html('<div class="alert alert-info">
            <strong>导入说明：</strong><br/>
            1. 支持 xls、xlsx 格式的Excel文件<br/>
            2. Excel文件只需要一列数据，包含E客码信息<br/>
            3. 第一行为标题行，从第二行开始读取数据<br/>
            4. 系统会自动过滤重复的E客码<br/>
            5. 请确保数据格式正确，避免导入失败
        </div>');

        $form->disableReset();
        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    /**
     * Excel导入处理
     */
    public function doImport(Content $content, Request $request)
    {
        $logger = Log::channel('optlog');

        $request->validate([
            'blok_goods_id' => 'required|integer',
            'excel_file' => 'required|mimes:xls,xlsx',
        ], [
            'blok_goods_id.required' => "请选择佰联ok商品",
            'excel_file.required' => "导入文件不能为空",
            'excel_file.mimes' => "上传文件格式为：xls，xlsx",
        ]);

        try {
            $blokGoodsId = $request->get('blok_goods_id');
            $blokGoods = BlokGoods::find($blokGoodsId);
            if (!$blokGoods) {
                $content->withError('错误', '选择的佰联ok商品不存在');
                return redirect('admin/blok-codes/import')->withInput();
            }

            // 处理文件
            $upFile = $request->file('excel_file');
            $ext = $upFile->getClientOriginalExtension();
            $orgName = $upFile->getClientOriginalName();
            $fileName = substr($orgName, 0, strripos($orgName, '.'));
            $name = $fileName . '_' . date('YmdHis') . '_' . sprintf('%04d', Admin::user()->id) . '.' . $ext;

            // 文件保存到storage/app/upload/blok_codes
            $path = $upFile->storeAs('upload' . DIRECTORY_SEPARATOR . 'blok_codes', $name);
            $url = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . $path;

            $logger->info('导入Blok码，' . $url);

            // 读取Excel文件
            $reader = IOFactory::createReader(ucfirst($ext));
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($url);
            $worksheet = $spreadsheet->getActiveSheet();

            $ecodes = [];
            $rowCount = 0;

            foreach ($worksheet->getRowIterator(2) as $row) { // 从第2行开始读取
                $rowIndex = $row->getRowIndex();
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false);

                $cells = [];
                foreach ($cellIterator as $cell) {
                    $cells[] = trim($cell->getFormattedValue());
                }

                // 取第一列的值作为E客码
                if (!empty($cells[0])) {
                    $ecode = $cells[0];
                    if (!in_array($ecode, $ecodes)) {
                        $ecodes[] = $ecode;
                    }
                }
                $rowCount++;

                // 防止内存溢出，限制最大行数
                if ($rowCount > 10000) {
                    break;
                }
            }

            if (empty($ecodes)) {
                $content->withError('提示', "Excel文件中没有找到有效的E客码数据");
                return redirect('admin/blok-codes/import')->withInput();
            }

            // 检查重复的E客码
            $existingEcodes = BlokCodes::where('blok_goods_id', $blokGoodsId)
                ->whereIn('ecode', $ecodes)
                ->pluck('ecode')
                ->toArray();

            $newEcodes = array_diff($ecodes, $existingEcodes);

            if (empty($newEcodes)) {
                $content->withError('提示', "所有E客码都已存在，没有新数据需要导入");
                return redirect('admin/blok-codes/import')->withInput();
            }

            // 准备插入数据
            $insertData = [];
            $now = now();
            $userId = Admin::user()->id;

            foreach ($newEcodes as $ecode) {
                $insertData[] = [
                    'blok_goods_id' => $blokGoodsId,
                    'ecode' => $ecode,
                    'state' => BlokCodes::STATE_UNUSED,
                    'created_by' => $userId,
                    'created_at' => $now,
                ];
            }

            // 批量插入数据
            DB::beginTransaction();
            try {
                BlokCodes::insert($insertData);
                DB::commit();

                $logger->info('Blok码导入成功，共导入' . count($insertData) . '条记录');

                $successMsg = sprintf(
                    '导入成功！共处理 %d 条E客码，新增 %d 条，重复 %d 条',
                    count($ecodes),
                    count($newEcodes),
                    count($existingEcodes)
                );

                $content->withSuccess('成功', $successMsg);
                return redirect('admin/blok-codes');

            } catch (\Exception $e) {
                DB::rollBack();
                $logger->error('Blok码导入失败：' . $e->getMessage());
                throw $e;
            }

        } catch (\Exception $e) {
            $logger->error('Blok码导入异常：' . $e->getMessage());
            $content->withError('错误', '导入失败：' . $e->getMessage());
            return redirect('admin/blok-codes/import')->withInput();
        }
    }
}
