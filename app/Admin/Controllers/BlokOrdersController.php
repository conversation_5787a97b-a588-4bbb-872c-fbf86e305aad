<?php

namespace App\Admin\Controllers;

use App\Models\BlokOrders;
use App\Models\OrderSub;
use App\Models\Order;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;

class BlokOrdersController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '核销记录管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new BlokOrders());

        $grid->column('id', __('ID'));

        // 关联显示订单信息
        $grid->column('order_no', __('订单号'));
        $grid->column('activity.activity_name', __('活动名称'));

        // 关联显示商品信息
        $grid->column('item_name', __('商品名称'));
        $grid->column('subOrder.goods_num', __('商品数量'));
        $grid->column('subOrder.goods_price', __('商品价格'))->display(function ($price) {
            return '¥' . number_format($price, 2);
        });

        // Blok订单信息
        $grid->column('ecode', __('E客码'));
        $grid->column('term_id', __('终端号'));
        $grid->column('term_type', __('终端类型'))->using([
            'ECR' => 'ECR',
            'APP' => 'APP',
            'MP' => 'MP'
        ])->label([
            'ECR' => 'primary',
            'APP' => 'success',
            'MP' => 'info'
        ]);

        $grid->column('txn_amt', __('支付金额'))->display(function ($amount) {
            return '¥' . number_format($amount, 2);
        });

        $grid->column('submit_status', __('核销状态'))->using([
            1 => '未核销',
            2 => '已核销',
            3 => '失败不再重提'
        ])->label([
            1 => 'warning',
            2 => 'success',
            3 => 'danger'
        ]);

        $grid->column('created_at', __('创建时间'))->sortable();
        $grid->column('submit_at', __('核销时间'))->sortable();

        // 禁用新增、编辑、删除操作（只读展示）
        $grid->disableCreateButton();

        // 配置操作列
        $grid->actions(function ($actions) {
            $actions->disableEdit();
            $actions->disableDelete();
            $actions->disableView();

            // 只对失败状态的订单显示重提按钮
            if ($actions->row->submit_status == BlokOrders::SUBMIT_STATUS_FAILED) {
                $actions->add(new \App\Admin\Actions\Grid\RetryBlokOrder);
            }
        });

        // 启用批量选择功能
        $grid->enableBatchActions();

        // 显示复选框列
        $grid->showCheckbox();

        // 添加自定义按钮到header
        $grid->header(function ($query) {
            $batchRetryUrl = admin_url('blok-orders/batch-retry');
            return <<<HTML
<div class="btn-group" style="margin-bottom: 10px;">
    <button type="button" class="btn btn-warning btn-sm" id="batch-retry-blok-orders">
        <i class="fa fa-refresh"></i>&nbsp;&nbsp;批量重提失败订单
    </button>
</div>

<script>
$(document).ready(function() {
    $('#batch-retry-blok-orders').on('click', function() {

        // 获取选中的订单ID
        var selected = [];
        $('.grid-row-checkbox:checked').each(function(){
            selected.push($(this).data('id'));
        });

        if (selected.length === 0) {
            swal('提示', '请先选择要重提的订单', 'warning');
            return;
        }

        swal({
            title: '批量重提确认',
            text: '确定要重提选中的 ' + selected.length + ' 个订单吗？',
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dd6b55',
            confirmButtonText: '确定重提',
            cancelButtonText: '取消',
            closeOnConfirm: false
        }, function(){

            $.ajax({
                method: 'post',
                url: '{$batchRetryUrl}',
                data: {
                    _token: LA.token,
                    ids: selected
                },
                success: function (data) {
                    if (typeof data === 'object') {
                        if (data.status) {
                            swal('成功', data.message, 'success');
                            // 刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            swal('失败', data.message, 'error');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    swal('错误', '请求失败，请重试', 'error');
                }
            });
        });
    });
});
</script>
HTML;
        });

        // 批量操作
        $grid->batchActions(function ($batch) {
            $batch->disableDelete();
            $batch->add(new \App\Admin\Actions\Grid\BatchRetryBlokOrders('批量重提'));
        });

        // 默认排序
        $grid->model()->orderBy('id', 'desc');

        // 查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();

            $filter->column(1 / 3, function (Filter $filter) {
                $filter->like('subOrder.order.order_no', '订单号');
            });

            $filter->column(1 / 3, function (Filter $filter) {
                $filter->like('ecode', 'E客码');
            });

            $filter->column(1 / 3, function (Filter $filter) {
                $filter->equal('submit_status', '核销状态')->select([
                    1 => '未核销',
                    2 => '已核销',
                    3 => '失败不再重提'
                ]);
            });

            $filter->column(1 / 3, function (Filter $filter) {
                $filter->like('subOrder.goods.goods_name', '商品名称');
            });


        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(BlokOrders::findOrFail($id));

        $show->field('id', __('ID'));

        // 订单信息
        $show->divider('订单信息');
        $show->field('subOrder.order.order_no', __('订单号'));
        $show->field('activity_id', __('活动ID'));
        $show->field('activity.activity_name', __('活动名称'));
        $show->field('subOrder.order.status', __('订单状态'));
        $show->field('subOrder.order.created_at', __('订单创建时间'));

        // 商品信息
        $show->divider('商品信息');
        $show->field('subOrder.goods.goods_name', __('商品名称'));
        $show->field('subOrder.goods.goods_no', __('商品编号'));
        $show->field('subOrder.goods_num', __('商品数量'));
        $show->field('subOrder.goods_price', __('商品价格'))->as(function ($price) {
            return '¥' . number_format($price, 2);
        });

        // 核销信息
        $show->divider('核销信息');
        $show->field('ecode', __('E客码'));
        $show->field('term_id', __('终端号'));
        $show->field('term_type', __('终端类型'));
        $show->field('request_id', __('请求ID'));
        $show->field('order_no', __('商户订单号'));

        // 金额信息
        $show->divider('金额信息');
        $show->field('txn_amt', __('支付订单总金额'))->as(function ($amount) {
            return '¥' . number_format($amount, 2);
        });
        $show->field('ord_amt', __('购物车订单总额'))->as(function ($amount) {
            return '¥' . number_format($amount, 2);
        });
        $show->field('item_req_amt', __('商品待核销金额'))->as(function ($amount) {
            return '¥' . number_format($amount, 2);
        });

        // 商品详情
        $show->divider('商品详情');
        $show->field('item_name', __('商品名称'));
        $show->field('item_no', __('购物车行号'));
        $show->field('item_category', __('商品品类'));
        $show->field('item_uni_code', __('商品编码'));
        $show->field('item_num', __('商品数量'));

        // 状态信息
        $show->divider('状态信息');
        $show->field('submit_status', __('提交状态'))->using([
            1 => '未提交',
            2 => '已提交'
        ]);
        $show->field('created_at', __('创建时间'));
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * 批量重提失败订单
     */
    public function batchRetry(\Illuminate\Http\Request $request)
    {
        $ids = $request->get('ids');

        if (empty($ids)) {
            return response()->json([
                'status' => false,
                'message' => '请选择要重提的订单'
            ]);
        }

        $successCount = 0;
        $failedCount = 0;
        $skippedCount = 0;

        foreach ($ids as $id) {
            try {
                $order = BlokOrders::find($id);

                if (!$order) {
                    $skippedCount++;
                    continue;
                }

                // 只处理失败状态的订单
                if ($order->submit_status != BlokOrders::SUBMIT_STATUS_FAILED) {
                    $skippedCount++;
                    continue;
                }

                // 将状态改为未提交
                $order->update([
                    'submit_status' => BlokOrders::SUBMIT_STATUS_PENDING,
                    'submit_at' => null,
                    'updated_at' => now()
                ]);

                $successCount++;

            } catch (\Exception $e) {
                $failedCount++;
                \Log::error("批量重提订单 {$id} 失败: " . $e->getMessage());
            }
        }

        $message = "批量重提完成！成功: {$successCount}";
        if ($skippedCount > 0) {
            $message .= "，跳过: {$skippedCount}";
        }
        if ($failedCount > 0) {
            $message .= "，失败: {$failedCount}";
        }

        return response()->json([
            'status' => true,
            'message' => $message
        ]);
    }
}
