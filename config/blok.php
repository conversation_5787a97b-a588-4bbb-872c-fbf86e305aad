<?php
/*
 * @Author: yangy <EMAIL>
 * @Date: 2025-07-08 11:11:46
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2025-07-08 16:18:30
 * @FilePath: /gift_backend/config/blok.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

return [
    /*
    |--------------------------------------------------------------------------
    | 佰联ok配置
    |--------------------------------------------------------------------------
    |
    | 佰联ok渠道相关配置信息
    |
    */

    // API域名
    'domain' => env('BLOK_API_DOMAIN', 'https://testokfep.okcard.com'),
    // 'domain' => env('BLOK_API_DOMAIN', 'https://syt.okcard.com'),

    // 商户号
    'merc_id' => env('BLOK_MERC_ID', 'merc_id'),

    // 门店号
    'store_id' => env('BLOK_STORE_ID', 'sdsd'),

    // 接入受理渠道
    'acqcnl' => env('BLOK_ACQCNL', 'acqcnl'),

    // SM2私钥 (用于签名)
    'private_key' => env('BLOK_PRIVATE_KEY', ''),

    // 对方SM2公钥 (用于加密)
    'public_key' => env('BLOK_PUBLIC_KEY', 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAECdKCpp7BjwTHtMX6xWs3RkJM289rMQWHkGCPEuoXGKyJLLDiFNbqhMcpfVurMlXtZYq2UMdpgQVld09cxBeMrQ=='),

    // 我方SM2公钥 (提供给对方验签用)
    'our_public_key' => env('BLOK_OUR_PUBLIC_KEY', '123456'),

    // 对方私钥 (用于解密，通常不需要)
    'their_private_key' => env('BLOK_THEIR_PRIVATE_KEY', '32323'),

    // 请求超时时间 (秒)
    'timeout' => env('BLOK_TIMEOUT', 30),

    // 重试超时时间 (分钟) - 超过此时间的未提交订单将标记为失败
    'retry_timeout' => env('BLOK_RETRY_TIMEOUT', 60),

    // 是否启用SSL验证
    'verify_ssl' => env('BLOK_VERIFY_SSL', false),

    // 是否启用调试模式
    'debug' => env('BLOK_DEBUG', false),

    // 每次处理的订单数量
    'batch_size' => env('BLOK_BATCH_SIZE', 50),

    // 重试次数
    'retry_times' => env('BLOK_RETRY_TIMES', 3),

    // 重试间隔 (秒)
    'retry_interval' => env('BLOK_RETRY_INTERVAL', 60),
];
