<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 佰联ok配置
    |--------------------------------------------------------------------------
    |
    | 佰联ok渠道相关配置信息
    |
    */

    // API域名
    'domain' => env('BLOK_API_DOMAIN', 'https://testokfep.okcard.com'),

    // 商户号
    'merc_id' => env('BLOK_MERC_ID', ''),

    // 门店号
    'store_id' => env('BLOK_STORE_ID', ''),

    // 接入受理渠道
    'acqcnl' => env('BLOK_ACQCNL', ''),

    // SM2私钥 (用于签名)
    'private_key' => env('BLOK_PRIVATE_KEY', ''),

    // 对方SM2公钥 (用于加密)
    'public_key' => env('BLOK_PUBLIC_KEY', ''),

    // 我方SM2公钥 (提供给对方验签用)
    'our_public_key' => env('BLOK_OUR_PUBLIC_KEY', ''),

    // 对方私钥 (用于解密，通常不需要)
    'their_private_key' => env('BLOK_THEIR_PRIVATE_KEY', ''),

    // 请求超时时间 (秒)
    'timeout' => env('BLOK_TIMEOUT', 30),

    // 是否启用SSL验证
    'verify_ssl' => env('BLOK_VERIFY_SSL', false),

    // 是否启用调试模式
    'debug' => env('BLOK_DEBUG', false),

    // 每次处理的订单数量
    'batch_size' => env('BLOK_BATCH_SIZE', 50),

    // 重试次数
    'retry_times' => env('BLOK_RETRY_TIMES', 3),

    // 重试间隔 (秒)
    'retry_interval' => env('BLOK_RETRY_INTERVAL', 60),
];
