<?php

use App\Logging\CustomizeFormatter;
use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [

        'optlog' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/optlog.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'admin_special_optlog' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/admin_special_optlog.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //记录业务日志 名字自己命名
        // 统一格式  利于ELK分析日志
        'api_log'              => [
            'driver' => 'daily',
            'path'   => storage_path('logs/api/api_log.log'),
            'level'  => 'info',
            'days'   => 0,
            'tap'    => [App\Logging\CustomizeFormatter::class],
        ],

        //黑名单日志记录
        'black_log'            => [
            'driver' => 'daily',
            'path'   => storage_path('logs/api/black_log.log'),
            'level'  => 'info',
            'days'   => 0,
            'tap'    => [App\Logging\CustomizeFormatter::class],
        ],

        //项目往外发送的http请求记录可以放在这里。
        'http_req_log'         => [
            'driver' => 'daily',
            'path'   => storage_path('logs/http_req/req_log.log'),
            'level'  => 'info',
            'days'   => 0,
            'tap'    => [App\Logging\CustomizeFormatter::class],
        ],

        //短信验证码记录
        'sms_captcha_log'      => [
            'driver' => 'daily',
            'path'   => storage_path('logs/captcha/captcha_log.log'),
            'level'  => 'info',
            'days'   => 0,
            'tap'    => [App\Logging\CustomizeFormatter::class],
        ],

        //图片验证码（前端）
        'img_captcha_log'      => [
            'driver' => 'daily',
            'path'   => storage_path('logs/captcha_img/captcha.log'),
            'level'  => 'info',
            'days'   => 0,
            'tap'    => [App\Logging\CustomizeFormatter::class],
        ],

        //记录sql语句
        'sql_log'              => [
            'driver' => 'daily',
            'path'   => storage_path('logs/sql/sql.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //记录订单提交记录
        'submit_order'         => [
            'driver' => 'daily',
            'path'   => storage_path('logs/channel/submit_order.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //记录Blok订单提交记录
        'blok_orders_submit'   => [
            'driver' => 'daily',
            'path'   => storage_path('logs/blok/submit_orders.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //记录订单查询记录
        'query_order'          => [
            'driver' => 'daily',
            'path'   => storage_path('logs/channel/query_order.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //订单状态回调
        'order_callback'      => [
            'driver' => 'daily',
            'path'   => storage_path('logs/channel/order_callback.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //余额查询
        'channel_balance'          => [
            'driver' => 'daily',
            'path'   => storage_path('logs/channel/balance.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'send_sms'            => [
            'driver' => 'daily',
            'path'   => storage_path('logs/sms/send_sms.log'),
            'level'  => 'info',
            'days'   => 0,
        ],
        //发送实物订单短信
        'send_entity_sms'     => [
            'driver' => 'daily',
            'path'   => storage_path('logs/sms/send_entity_sms.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //短信回调
        'sms_callback'        => [
            'driver' => 'daily',
            'path'   => storage_path('logs/sms/callback.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //主订单状态更改
        'master_order_update' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/master_order_update.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //支付公共方法日志
        'trade_log'           => [
            'driver' => 'daily',
            'path'   => storage_path('logs/trade_log.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //支付公共方法接受回调日志
        'trade_callback'      => [
            'driver' => 'daily',
            'path'   => storage_path('logs/trade_callback.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //支付公共方法查询充值结果日志
        'trade_query'         => [
            'driver' => 'daily',
            'path'   => storage_path('logs/trade_query.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //短信发送
        'sms_send'            => [
            'driver' => 'daily',
            'path'   => storage_path('logs/sms/send.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'order_to_sms_result' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/sms/order_to_sms_result.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'exchange_log' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/exchange/exchange.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'batch_monitor' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/monitor/batch_monitor.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'ningde_count' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/monitor/ningde_count.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'ningde_monitor' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/monitor/ningde_monitor.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'exchange_forbidden_log' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/exchange/forbidden.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'bl_import_log' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/bl/import/import.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'bl_api_log' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/bl/api/api.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'bl_settlement_log' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/bl/settlement/settlement.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        //光大工会活动2023-07-11
        'cebgh_log' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/cebgh/cebgh.log'),
            'level'  => 'info',
            'days'   => 0,
        ],

        'stack' => [
            'driver'            => 'stack',
            'channels'          => ['single', 'daily'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path'   => storage_path('logs/laravel.log'),
            'level'  => env('LOG_LEVEL_SINGLE', 'error'),
        ],

        'daily' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/laravel.log'),
            'level'  => env('LOG_LEVEL_DAILY', 'info'),
            'days'   => env('LOG_LEVEL_DAILY_DAYS', 30),
        ],

        'slack' => [
            'driver'   => 'slack',
            'url'      => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji'    => ':boom:',
            'level'    => 'critical',
        ],

        'papertrail' => [
            'driver'       => 'monolog',
            'level'        => 'debug',
            'handler'      => SyslogUdpHandler::class,
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
            ],
        ],

        'stderr' => [
            'driver'    => 'monolog',
            'handler'   => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with'      => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level'  => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level'  => 'debug',
        ],

        'null' => [
            'driver'  => 'monolog',
            'handler' => NullHandler::class,
        ],

        'emergency' => [
            'path' => storage_path('logs/laravel.log'),
        ],

        'spe_log'     => [
            'driver' => 'daily',
            'path'   => storage_path('logs/spe/spe_order.log'),
            'level'  => 'info',
            'days'   => 0,
            'tap'    => [App\Logging\CustomizeFormatter::class],
        ],
        'submit_qcp2' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/qcp2/submit.log'),
            'level'  => 'info',
            'days'   => 0,
        ],
    ],

];
