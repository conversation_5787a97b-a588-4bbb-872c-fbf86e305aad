<div class="btn-group" style="margin-bottom: 10px;">
    <button type="button" class="btn btn-warning btn-sm" id="batch-retry-blok-orders">
        <i class="fa fa-refresh"></i>&nbsp;&nbsp;批量重提失败订单
    </button>
</div>

<script>
$(document).ready(function() {
    $('#batch-retry-blok-orders').on('click', function() {
        
        // 获取选中的订单ID
        var selected = [];
        $('.grid-row-checkbox:checked').each(function(){
            selected.push($(this).data('id'));
        });
        
        if (selected.length === 0) {
            swal('提示', '请先选择要重提的订单', 'warning');
            return;
        }
        
        swal({
            title: '批量重提确认',
            text: '确定要重提选中的 ' + selected.length + ' 个订单吗？',
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dd6b55',
            confirmButtonText: '确定重提',
            cancelButtonText: '取消',
            closeOnConfirm: false
        }, function(){
            
            $.ajax({
                method: 'post',
                url: '{{ admin_url("blok-orders/batch-retry") }}',
                data: {
                    _token: LA.token,
                    ids: selected
                },
                success: function (data) {
                    if (typeof data === 'object') {
                        if (data.status) {
                            swal('成功', data.message, 'success');
                            // 刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            swal('失败', data.message, 'error');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    swal('错误', '请求失败，请重试', 'error');
                }
            });
        });
    });
});
</script>
