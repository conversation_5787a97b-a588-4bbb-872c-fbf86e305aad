# OrderRealSuccessEvent 功能实现

## 功能概述

当订单状态更新为成功时，自动处理Blok相关的业务逻辑，包括分配E客码和创建Blok订单记录。

## 业务流程

### 触发条件
- `Order::dealStatus($id)` 方法被调用
- 订单状态更新为已发货 (`SysCode::ORDER_STATUS_2`)

### 处理流程
1. **触发事件**: 在Order模型中触发`OrderRealSuccessEvent`
2. **事件处理**: `HandleOrderRealSuccessListener`监听并处理事件
3. **查找配置**: 根据`activity_id`和`goods_id`在`blok_settings`表中查找配置
4. **分配E客码**: 从`blok_codes`表中获取第一个未使用的E客码
5. **创建记录**: 在`blok_orders`表中创建新记录
6. **更新状态**: 将E客码状态更新为已使用

## 数据流转

```
Order (activity_id) + OrderSub (goods_id)
    ↓
BlokSettings (activity_id, goods_id) → blok_goods_id
    ↓
BlokCodes (blok_goods_id) → ecode (第一个未使用的)
    ↓
BlokOrders (sub_order_id, ecode, 完整订单信息)
```

## 核心文件

### 1. Event类 (已存在)
- **文件**: `app/Events/OrderRealSuccessEvent.php`
- **作用**: 定义订单成功事件

### 2. Listener类 (新建)
- **文件**: `app/Listeners/HandleOrderRealSuccessListener.php`
- **作用**: 处理订单成功事件的业务逻辑
- **核心方法**:
  - `handle()`: 事件处理入口
  - `processBlokOrders()`: 处理Blok订单逻辑
  - `createBlokOrder()`: 创建Blok订单记录

### 3. BlokOrders模型 (新建)
- **文件**: `app/Models/BlokOrders.php`
- **作用**: 管理blok_orders表的数据操作
- **特性**:
  - 完整的字段定义
  - 状态常量定义
  - 关联关系定义

### 4. Order模型 (修改)
- **文件**: `app/Models/Order.php`
- **修改内容**: 在`dealStatus()`方法中添加事件触发逻辑

### 5. EventServiceProvider (修改)
- **文件**: `app/Providers/EventServiceProvider.php`
- **修改内容**: 注册Event和Listener的映射关系

## 关键特性

### 1. 同步队列处理
- 使用Laravel的事件系统
- 同步处理确保数据一致性
- 完整的错误处理机制

### 2. 智能E客码分配
- 自动查找未使用的E客码
- 按blok_goods_id精确匹配
- 分配后自动更新状态

### 3. 完整订单信息
- 自动生成终端号和请求ID
- 包含商品详细信息
- 支持多种终端类型

### 4. 错误处理
- 详细的日志记录
- 异常情况的优雅处理
- 不影响主业务流程

## 数据库表结构

### blok_orders表字段说明
- `sub_order_id`: 子订单ID (关联order_subs.id)
- `ecode`: E客码
- `term_id`: 终端号 (自动生成)
- `term_type`: 终端类型 (ECR/APP/MP)
- `request_id`: 商户支付订单号 (自动生成)
- `order_no`: 商户购物车订单号
- `txn_amt`: 支付订单总金额
- `ord_amt`: 购物车订单总额
- `item_name`: 商品名称
- `item_no`: 购物车行号
- `item_category`: 商品品类
- `item_uni_code`: 商品编码
- `item_num`: 商品数量
- `item_req_amt`: 商品待核销金额
- `submit_status`: 提交状态 (1:未提交 2:已提交)

## 使用示例

### 触发事件
```php
// 在Order::dealStatus()方法中
if ($order->status == SysCode::ORDER_STATUS_2) {
    event(new OrderRealSuccessEvent(['order_id' => $order->id]));
}
```

### 查看处理结果
```sql
-- 查看生成的Blok订单
SELECT * FROM blok_orders WHERE sub_order_id = ?;

-- 查看E客码使用状态
SELECT * FROM blok_codes WHERE state = 2; -- 已使用
```

## 监控和调试

### 日志位置
- Laravel日志: `storage/logs/laravel.log`
- 关键日志标识: `OrderRealSuccessEvent`

### 调试要点
1. 确认事件是否正确触发
2. 检查blok_settings配置是否存在
3. 验证blok_codes是否有可用E客码
4. 确认blok_orders记录是否正确创建

## 扩展性

### 支持的扩展
- 多种终端类型支持
- 灵活的商品品类映射
- 可配置的ID生成规则
- 支持批量处理

### 未来优化方向
- 异步队列处理
- E客码预分配机制
- 更详细的业务规则配置
- 支持回滚机制

这个实现提供了完整的订单成功后的Blok业务处理能力，具有良好的扩展性和维护性。
