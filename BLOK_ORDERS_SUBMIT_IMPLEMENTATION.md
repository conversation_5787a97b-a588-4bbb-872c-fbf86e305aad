# Blok订单提交功能实现

## 功能概述

实现将`blok_orders`表中的数据提交到佰联ok渠道的5.1消费接口，提交成功后更新`submit_status`状态。

## 核心组件

### 1. 定时任务命令
**文件**: `app/Console/Commands/SubmitBlokOrders.php`

**功能**:
- 继承`SignalAndLockedBaseCommand`，支持锁机制
- 每次处理50条未提交的订单
- 调用`BlokOrderSubmitService`处理具体提交逻辑
- 完整的错误处理和日志记录

**调度配置**: 每5分钟执行一次

### 2. 订单提交服务
**文件**: `app/Service/BlokOrderSubmitService.php`

**功能**:
- 构建符合佰联ok接口规范的请求数据
- 实现SM2签名和加密机制
- 发送HTTP请求到佰联ok接口
- 解析响应结果并返回处理状态

### 3. 配置管理
**文件**: `config/blok.php`

**包含配置**:
- API域名和商户信息
- SM2密钥配置
- 请求参数配置
- 批处理和重试配置

## 接口对接详情

### 5.1 消费接口规范
- **地址**: `${domain}/okfep/trans/v5/sale`
- **方法**: POST
- **格式**: JSON
- **安全**: SM2签名 + SM2加密

### 请求数据结构
```json
{
  "service": "sale",
  "mercId": "商户号",
  "storeId": "门店号", 
  "termId": "终端号",
  "termType": "终端类型(ECR/APP/MP)",
  "requestId": "商户支付订单号",
  "orderNo": "商户购物车订单号",
  "txnAmt": 支付订单总金额,
  "ordAmt": 购物车订单总额,
  "payCodeList": ["E客码"],
  "goodsList": [
    {
      "itemNo": 购物车行号,
      "itemName": "商品名称",
      "itemCategory": "商品品类",
      "itemUniCode": "商品编码", 
      "itemPrice": 商品单价,
      "itemNum": 商品数量,
      "itemReqAmt": 商品待核销金额
    }
  ]
}
```

### 响应处理
- **成功标识**: `retCode = "00000000"`
- **成功处理**: 更新`submit_status = 2`
- **失败处理**: 记录错误信息，保持原状态

## 数据映射

### blok_orders → 请求数据
| blok_orders字段 | 请求字段 | 说明 |
|----------------|----------|------|
| term_id | termId | 终端号 |
| term_type | termType | 终端类型 |
| request_id | requestId | 商户支付订单号 |
| order_no | orderNo | 商户购物车订单号 |
| txn_amt | txnAmt | 支付订单总金额 |
| ord_amt | ordAmt | 购物车订单总额 |
| ecode | payCodeList[0] | E客码 |
| item_no | goodsList[0].itemNo | 购物车行号 |
| item_name | goodsList[0].itemName | 商品名称 |
| item_category | goodsList[0].itemCategory | 商品品类 |
| item_uni_code | goodsList[0].itemUniCode | 商品编码 |
| item_num | goodsList[0].itemNum | 商品数量 |
| item_req_amt | goodsList[0].itemReqAmt | 商品待核销金额 |

## 安全机制

### SM2签名验签
1. 使用私钥对整个请求报文进行SM2WithSM3签名
2. 将签名结果放在HTTP头的`sign`字段中
3. 对方使用公钥进行验签

### SM2加密解密
1. 使用对方公钥对整个请求报文进行SM2加密
2. 加密后的数据作为HTTP Body发送
3. 使用私钥解密响应数据

## 环境配置

### 必需的环境变量
```env
# 基础配置
BLOK_API_DOMAIN=https://testokfep.okcard.com
BLOK_MERC_ID=商户号
BLOK_STORE_ID=门店号
BLOK_ACQCNL=接入受理渠道

# 密钥配置
BLOK_PRIVATE_KEY=SM2私钥
BLOK_PUBLIC_KEY=对方SM2公钥
BLOK_OUR_PUBLIC_KEY=我方SM2公钥

# 可选配置
BLOK_TIMEOUT=30
BLOK_BATCH_SIZE=50
BLOK_DEBUG=false
```

## 调度配置

### Kernel.php配置
```php
// 提交Blok订单到渠道，每5分钟执行一次
$this->schedule_command($schedule, 'ap:submit-blok-orders', 'everyFiveMinutes');
```

### 控制开关
```env
# 启用/禁用命令
CMD_AP_SUBMIT_BLOK_ORDERS=true

# 执行频率(秒)
CMD_FREQUENCY_AP_SUBMIT_BLOK_ORDERS=300
```

## 日志配置

### 专用日志通道
```php
'blok_orders_submit' => [
    'driver' => 'daily',
    'path' => storage_path('logs/blok/submit_orders.log'),
    'level' => 'info',
    'days' => 0,
],
```

## 监控和调试

### 关键监控点
1. **日志文件**: `storage/logs/blok/submit_orders.log`
2. **命令执行**: `php artisan ap:submit-blok-orders`
3. **数据库状态**: 查询`submit_status=1`的记录数量
4. **API响应**: 监控接口调用成功率

### 调试命令
```bash
# 手动执行命令
php artisan ap:submit-blok-orders

# 查看日志
tail -f storage/logs/blok/submit_orders.log

# 检查待提交订单
SELECT COUNT(*) FROM blok_orders WHERE submit_status = 1;
```

## 扩展性设计

### 支持的扩展
- 多商户配置
- 重试机制
- 批量提交优化
- 异步处理
- 监控告警

### 待完善功能
1. **SM2加密实现**: 当前为模拟实现，需要集成真实的SM2库
2. **重试机制**: 失败订单的重试处理
3. **监控告警**: 失败率过高时的告警机制
4. **性能优化**: 批量提交和并发处理

## 测试验证

### 测试步骤
1. 配置测试环境变量
2. 创建测试数据到`blok_orders`表
3. 手动执行命令验证功能
4. 检查日志和数据库状态
5. 验证接口调用和响应处理

### 测试数据
确保`blok_orders`表中有`submit_status=1`的测试记录，包含完整的订单信息。

这个实现提供了完整的Blok订单提交能力，具有良好的扩展性、可维护性和监控能力。
