# Blok事件测试指南

## 🧪 **测试命令使用方法**

### 1. **自动查找订单测试**
```bash
php artisan test:blok-event
```
系统会自动查找一个合适的测试订单进行测试。

### 2. **指定订单ID测试**
```bash
php artisan test:blok-event 12345
```
使用指定的订单ID进行测试。

## 📋 **测试流程**

### 1. **订单信息展示**
- 显示订单基本信息（ID、订单号、活动ID、状态）
- 显示所有子订单详情（商品ID、商品名称、数量、价格）

### 2. **前置条件检查**
- ✅ 检查BlokSettings配置是否存在
- ✅ 检查对应的E客码库存是否充足
- ❌ 如果条件不满足，会显示具体缺失的内容

### 3. **事件触发**
- 触发`OrderRealSuccessEvent`事件
- 捕获并显示任何异常信息

### 4. **结果验证**
- 检查是否生成了BlokOrders记录
- 显示生成的E客码和相关信息
- 验证E客码状态是否正确更新

## 🎯 **测试数据要求**

### **必需的数据**
1. **订单数据**：
   - 状态为2（成功）的订单
   - 订单必须有`activity_id`
   - 订单必须有子订单（order_subs）

2. **配置数据**：
   - `blok_settings`表中有对应的配置记录
   - 配置包含：`activity_id` + `goods_id` → `blok_goods_id`

3. **E客码数据**：
   - `blok_codes`表中有可用的E客码
   - E客码状态为1（未使用）
   - E客码的`blok_goods_id`与配置匹配

### **检查现有数据的SQL**
```sql
-- 1. 检查可用的测试订单
SELECT o.id, o.order_no, o.activity_id, o.status, COUNT(os.id) as sub_count
FROM orders o 
LEFT JOIN order_subs os ON o.id = os.order_id 
WHERE o.status = 2 AND o.activity_id IS NOT NULL
GROUP BY o.id 
HAVING sub_count > 0 
ORDER BY o.id DESC
LIMIT 10;

-- 2. 检查BlokSettings配置
SELECT bs.*, a.activity_name, g.goods_name, bg.name as blok_goods_name
FROM blok_settings bs
LEFT JOIN activities a ON bs.activity_id = a.id
LEFT JOIN goods g ON bs.goods_id = g.id  
LEFT JOIN blok_goods bg ON bs.blok_goods_id = bg.id;

-- 3. 检查E客码库存
SELECT bc.blok_goods_id, bg.name, COUNT(*) as available_count
FROM blok_codes bc
LEFT JOIN blok_goods bg ON bc.blok_goods_id = bg.id
WHERE bc.state = 1
GROUP BY bc.blok_goods_id, bg.name
ORDER BY available_count DESC;
```

## 📊 **测试输出示例**

```
=== Blok事件测试开始 ===
正在查找合适的测试订单...

=== 订单信息 ===
订单ID: 12345
订单号: ORD20250708001
活动ID: 100
订单状态: 2
子订单数量: 2

=== 子订单详情 ===
子订单1:
  ID: 67890
  商品ID: 200
  商品名称: 测试商品A
  商品数量: 1
  商品价格: 99.00

=== 检查前置条件 ===
✅ 找到配置: 活动100 + 商品200 → Blok商品300
✅ 可用E客码数量: 50

是否继续执行事件测试？ (yes/no) [no]:
> yes

=== 触发事件 ===
触发OrderRealSuccessEvent...
✅ 事件触发成功

=== 检查处理结果 ===
子订单1 (ID: 67890):
  ✅ 已生成Blok订单
    Blok订单ID: 123
    E客码: BLK20250708001
    终端号: TERM_20250708001
    终端类型: APP
    请求ID: REQ_67890_1704700800
    提交状态: 未提交
    E客码状态: 已使用

=== Blok事件测试完成 ===
```

## 🔍 **故障排除**

### **常见问题**

1. **没有找到合适的测试订单**
   - 检查是否有状态为2的订单
   - 确保订单有activity_id和子订单

2. **未找到配置**
   - 在blok_settings表中添加对应配置
   - 确保activity_id和goods_id匹配

3. **没有可用的E客码**
   - 通过Excel导入功能添加E客码
   - 确保blok_goods_id匹配

4. **事件触发失败**
   - 检查日志文件：`storage/logs/laravel.log`
   - 查看具体的错误信息

### **日志查看**
```bash
# 查看Laravel日志
tail -f storage/logs/laravel.log

# 查看Blok专用日志
tail -f storage/logs/blok/submit_orders.log
```

## 🎯 **测试建议**

1. **首次测试**：使用自动查找模式
2. **重复测试**：可以多次运行同一订单测试
3. **数据清理**：测试后可以删除生成的BlokOrders记录
4. **日志监控**：测试时同时查看日志输出

## 🚀 **测试完成后**

测试成功后，您可以：
1. 验证定时任务：`php artisan ap:submit-blok-orders`
2. 检查订单提交状态
3. 查看完整的业务流程

这个测试命令提供了完整的事件测试能力，帮助您验证Blok业务流程的正确性！
