# BlokSettings商品选择优化

## 优化内容

### 问题描述
原来的商品选择显示所有启用的商品，不够精确。用户希望选择某个活动后，只显示该活动配置的商品。

### 解决方案
通过`activity_prizes`表建立活动和商品的关联关系，实现动态商品加载。

## 技术实现

### 1. Controller优化
在`BlokSettingsController.php`中：

#### 修改表单商品选择
```php
$form->select('goods_id', __('商品'))
    ->options(function ($id) {
        if ($id) {
            // 编辑时，获取当前记录的商品信息
            $setting = BlokSettings::find($id);
            if ($setting && $setting->goods) {
                return [$setting->goods_id => $setting->goods->goods_name];
            }
        }
        return [];
    })
    ->ajax('/admin/blok-settings/goods-by-activity')
    ->required()
    ->help('请先选择活动，然后选择该活动中配置的商品');
```

#### 新增API方法
```php
public function getGoodsByActivity(Request $request)
{
    $activityId = $request->get('q');
    
    if (!$activityId) {
        return [];
    }

    $goods = DB::table('activity_prizes')
        ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
        ->where('activity_prizes.activity_id', $activityId)
        ->where('goods.status', 1)
        ->orderBy('activity_prizes.order_by')
        ->orderBy('activity_prizes.id', 'desc')
        ->get(['goods.id', 'goods.goods_name']);

    $ret = [];
    foreach ($goods as $g) {
        $ret[] = ['id' => $g->id, 'text' => $g->goods_name];
    }

    return $ret;
}
```

### 2. 路由配置
在`app/Admin/routes.php`中添加：
```php
// Blok设置相关API
$router->get('blok-settings/goods-by-activity', 'BlokSettingsController@getGoodsByActivity');
```

### 3. 数据库关联
- `activities` 表：活动信息
- `activity_prizes` 表：活动和商品的关联关系
- `goods` 表：商品信息

查询逻辑：
```sql
SELECT goods.id, goods.goods_name 
FROM activity_prizes 
LEFT JOIN goods ON activity_prizes.goods_id = goods.id 
WHERE activity_prizes.activity_id = ? 
AND goods.status = 1 
ORDER BY activity_prizes.order_by, activity_prizes.id DESC
```

## 功能特点

### 1. 动态加载
- 用户选择活动后，商品下拉框自动更新
- 只显示该活动配置的商品
- 使用Ajax异步加载，用户体验良好

### 2. 编辑支持
- 编辑现有配置时，正确显示已选择的商品
- 支持修改活动后重新加载商品列表

### 3. 数据验证
- 确保只能选择活动中配置的商品
- 防止无效的商品配置

### 4. 排序支持
- 按照`activity_prizes.order_by`字段排序
- 保持与活动奖品配置一致的显示顺序

## 使用流程

1. **访问页面**：进入`/admin/blok-settings`
2. **新增配置**：点击新增按钮
3. **选择活动**：从活动下拉框选择目标活动
4. **选择商品**：商品下拉框自动加载该活动的商品列表
5. **选择佰联ok商品**：选择对应的佰联ok商品
6. **保存配置**：完成活动、商品、佰联ok商品的关联配置

## 优势

1. **精确性**：只显示活动相关的商品，避免选择错误
2. **易用性**：自动加载，减少用户操作步骤
3. **一致性**：与系统其他模块的商品选择逻辑保持一致
4. **可维护性**：基于现有的数据结构，无需额外的表结构修改

## 兼容性

- 完全兼容现有的数据结构
- 不影响已有的配置数据
- 支持Laravel-admin的标准功能

这次优化使BlokSettings的商品选择更加精确和用户友好，提升了配置的准确性和操作体验。
