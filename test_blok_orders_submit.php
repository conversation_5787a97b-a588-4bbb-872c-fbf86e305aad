<?php
/**
 * 测试Blok订单提交功能的脚本
 */

echo "Testing Blok Orders Submit Functionality...\n";

try {
    echo "\n=== 功能概述 ===\n";
    echo "1. 定时任务扫描blok_orders表中未提交的订单\n";
    echo "2. 调用佰联ok的5.1消费接口提交订单\n";
    echo "3. 根据返回结果更新submit_status状态\n";
    echo "4. 完整的错误处理和日志记录\n";

    echo "\n=== 核心组件 ===\n";
    echo "✓ SubmitBlokOrders Command - 定时任务命令\n";
    echo "✓ BlokOrderSubmitService - 订单提交服务\n";
    echo "✓ config/blok.php - 配置文件\n";
    echo "✓ logs/blok/submit_orders.log - 专用日志\n";

    echo "\n=== 接口对接 ===\n";
    echo "接口地址: {domain}/okfep/trans/v5/sale\n";
    echo "请求方法: POST\n";
    echo "数据格式: JSON\n";
    echo "安全机制: SM2签名 + SM2加密\n";

    echo "\n=== 请求数据结构 ===\n";
    echo "{\n";
    echo "  \"service\": \"sale\",\n";
    echo "  \"mercId\": \"商户号\",\n";
    echo "  \"storeId\": \"门店号\",\n";
    echo "  \"termId\": \"终端号\",\n";
    echo "  \"termType\": \"终端类型(ECR/APP/MP)\",\n";
    echo "  \"requestId\": \"商户支付订单号\",\n";
    echo "  \"orderNo\": \"商户购物车订单号\",\n";
    echo "  \"txnAmt\": 支付订单总金额,\n";
    echo "  \"ordAmt\": 购物车订单总额,\n";
    echo "  \"payCodeList\": [\"E客码\"],\n";
    echo "  \"goodsList\": [\n";
    echo "    {\n";
    echo "      \"itemNo\": 购物车行号,\n";
    echo "      \"itemName\": \"商品名称\",\n";
    echo "      \"itemCategory\": \"商品品类\",\n";
    echo "      \"itemUniCode\": \"商品编码\",\n";
    echo "      \"itemPrice\": 商品单价,\n";
    echo "      \"itemNum\": 商品数量,\n";
    echo "      \"itemReqAmt\": 商品待核销金额\n";
    echo "    }\n";
    echo "  ]\n";
    echo "}\n";

    echo "\n=== 响应处理 ===\n";
    echo "成功标识: retCode = \"00000000\"\n";
    echo "失败处理: 记录错误信息，不更新状态\n";
    echo "状态更新: submit_status = 2 (已提交)\n";

    echo "\n=== 调度配置 ===\n";
    echo "执行频率: 每5分钟执行一次\n";
    echo "批处理: 每次处理50条记录\n";
    echo "锁机制: 防止重复执行\n";
    echo "超时时间: 30分钟\n";

    echo "\n=== 环境变量配置 ===\n";
    echo "BLOK_API_DOMAIN=https://testokfep.okcard.com\n";
    echo "BLOK_MERC_ID=商户号\n";
    echo "BLOK_STORE_ID=门店号\n";
    echo "BLOK_ACQCNL=接入受理渠道\n";
    echo "BLOK_PRIVATE_KEY=SM2私钥\n";
    echo "BLOK_PUBLIC_KEY=对方SM2公钥\n";
    echo "BLOK_TIMEOUT=30\n";
    echo "BLOK_BATCH_SIZE=50\n";

    echo "\n=== 安全机制 ===\n";
    echo "1. SM2签名验签\n";
    echo "   - 使用私钥对请求数据签名\n";
    echo "   - 对方使用公钥验签\n";
    echo "\n";
    echo "2. SM2加密解密\n";
    echo "   - 使用对方公钥加密请求数据\n";
    echo "   - 使用私钥解密响应数据\n";

    echo "\n=== 数据流转 ===\n";
    echo "blok_orders (submit_status=1) → 构建请求数据 → 签名加密 → 发送请求\n";
    echo "                                ↓\n";
    echo "接收响应 → 解密验签 → 解析结果 → 更新状态 (submit_status=2)\n";

    echo "\n=== 监控要点 ===\n";
    echo "1. 日志文件: storage/logs/blok/submit_orders.log\n";
    echo "2. 命令状态: php artisan ap:submit-blok-orders\n";
    echo "3. 数据库状态: SELECT * FROM blok_orders WHERE submit_status = 1\n";
    echo "4. 错误统计: 查看日志中的ERROR级别信息\n";

    echo "\n=== 测试步骤 ===\n";
    echo "1. 配置环境变量\n";
    echo "2. 确保blok_orders表有测试数据\n";
    echo "3. 手动执行命令: php artisan ap:submit-blok-orders\n";
    echo "4. 检查日志输出\n";
    echo "5. 验证数据库状态更新\n";

    echo "\n=== 注意事项 ===\n";
    echo "⚠️  SM2加密解密需要实际实现\n";
    echo "⚠️  测试环境和生产环境配置不同\n";
    echo "⚠️  确保网络连通性\n";
    echo "⚠️  监控API调用频率限制\n";
    echo "⚠️  处理异常情况的重试机制\n";

    echo "\n=== 文件清单 ===\n";
    echo "✓ app/Console/Commands/SubmitBlokOrders.php\n";
    echo "✓ app/Service/BlokOrderSubmitService.php\n";
    echo "✓ config/blok.php\n";
    echo "✓ config/logging.php (添加日志配置)\n";
    echo "✓ app/Console/Kernel.php (添加调度任务)\n";

    echo "\n✓ Blok订单提交功能实现完成！\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}
